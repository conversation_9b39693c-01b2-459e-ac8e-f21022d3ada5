package com.thedasagroup.suminative.ui.products

import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenItem
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenRequest
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.PrintRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase
import com.thedasagroup.suminative.domain.courses_notification.SendCoursesNotificationUseCase
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase
import com.thedasagroup.suminative.domain.sales_report.PrintSalesReportUseCase
import com.thedasagroup.suminative.domain.table_sync.CreateOrUpdateOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.DeleteOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.GetSyncedOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.SyncOrderToTableUseCase
import com.thedasagroup.suminative.domain.table_sync.ToggleTableOccupiedUseCase
import com.thedasagroup.suminative.domain.table_sync.UpdateOrderForTableUseCase
import com.thedasagroup.suminative.ui.products.cart.CartTab
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase
import com.thedasagroup.suminative.ui.service.throttleFirst
import com.thedasagroup.suminative.ui.service.throttleLatest
import com.thedasagroup.suminative.ui.stock.StockUseCase
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.work.OrderSyncManager
import com.thedasagroup.suminative.work.SyncStatus
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.UUID

// Data class for Meal Course
data class MealCourse(
    val name: String,
    val sortOrder: Int
)

// Extended Cart item with meal course assignment
data class CartItemWithCourse(
    val cart: Cart,
    val courseId: String = "", // No default course initially
)

// Filter options for viewing courses
enum class CourseFilter(val displayName: String) {
    ALL("All"),
    STARTERS("Starters"),
    MAINS("Mains"),
    DESSERTS("Desserts")
}


class ProductsScreenViewModel @AssistedInject constructor(
    @Assisted state: ProductsScreenState,
    val prefs: Prefs,
    val stockUseCase: StockUseCase,
    val orderUseCase: PlaceOnlineOrderUseCase,
    val offlineOrderUseCase: OrderUseCase,
    val cloudPrintUseCase: CloudPrintUseCase,
    val getOptionDetailsUseCase: OptionDetailsUseCase,
    val salesUseCase: TotalSalesUseCase,
    val salesReportUseCase: GetSalesReportUseCase,
    val trueTimeImpl: TrueTimeImpl,
    val productsRepository: ProductRepository,
    val downloadProductsUseCase: DownloadProductsUseCase,
    val orderSyncManager: OrderSyncManager,
    val sendCoursesNotificationUseCase: SendCoursesNotificationUseCase,
    val printRepository: PrintRepository,
    val getSyncedOrderForTableUseCase: GetSyncedOrderForTableUseCase,
    val syncOrderToTableUseCase: SyncOrderToTableUseCase,
    val toggleTableOccupiedUseCase: ToggleTableOccupiedUseCase,
    val updateOrderForTableUseCase: UpdateOrderForTableUseCase,
    val deleteOrderForTableUseCase: DeleteOrderForTableUseCase,
    val createOrUpdateOrderForTableUseCase: CreateOrUpdateOrderForTableUseCase,
    val reservationsRepository: ReservationsRepository,
    val printSalesReportUseCase: PrintSalesReportUseCase,
) : MavericksViewModel<ProductsScreenState>(state) {

    var tableJob: Job? = null

    init {
        monitorSyncStatus()
    }

    suspend fun getStockItems(): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        setState {
            copy(stockItemsResponse = Loading())
        }
        stockUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(stockItemsResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(stockItemsResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        setState {
            copy(refreshing = true)
        }

        downloadProductsUseCase.refreshProducts().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        getStockItems()
                    }
                    // Refresh the stock items after successful product refresh
                    copy(refreshing = false)
                }

                else -> {
                    flow.value = Uninitialized
                    copy(refreshing = false)
                }
            }
        }
        return flow
    }

    suspend fun getTotalSales(request: SalesRequest): StateFlow<Async<SalesResponse>> {
        val flow = MutableStateFlow<Async<SalesResponse>>(Loading())
        setState {
            copy(salesResponse = Loading())
        }
        salesUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun getSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        setState {
            copy(salesReportResponse = Loading(), salesRequest = request)
        }
        salesReportUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesReportResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesReportResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateStock(
        order: Order, stock: Int, optionDetails: OptionDetails, stockItem: StoreItem,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item (this method is used for product details updates)
            // For now, keep the existing logic since this is used from product details screen
            if (it.storeItem?.id == stockItem.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            ) {
                it.copy(storeItem = updatedStock)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(
                    stock = stock,
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem,
                        optionSets = optionDetails.optionSets ?: mutableListOf(),
                        updatedStock = stock
                    ).billAmount ?: 0.0
                )
            } else {
                // Fallback to global cart
                copy(
                    stock = stock,
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem,
                        optionSets = optionDetails.optionSets ?: mutableListOf(),
                        updatedStock = stock
                    ).billAmount ?: 0.0
                )
            }
        }
    }


    fun resetStock() {
        setState {
            copy(
                stock = 1,
                optionDetailsResponse = Uninitialized,
                productTotal = 0.0
            )
        }
    }


    fun updateCartStock(
        state: ProductsScreenState,
        order: Order, stock: Int, stockItem: StoreItem, optionDetails: OptionDetails,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        cart: Cart? = null
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Use cart UUID if available, otherwise fall back to store item ID and options
            val isTargetItem = if (cart != null) {
                it.uuid == cart.uuid
            } else {
                it.storeItem?.id == stockItem.id &&
                        areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            }

            if (isTargetItem) {
                // Update the item-level totals based on new quantity
                val unitPrice = (it.netPayable ?: 0.0) / (it.quantity ?: 1)
                val unitTax = (it.tax ?: 0.0) / (it.quantity ?: 1)
                val newNetPayable = unitPrice * stock
                val newTax = unitTax * stock

                it.copy(
                    quantity = stock,
                    netPayable = newNetPayable,
                    tax = newTax,
                    storeItem = updatedStock
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumByDouble { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumByDouble { it.tax ?: 0.0 } ?: 0.0

        // Check if service charge is applied and include it in total
        val serviceChargeApplied = if (currentTableId != null) {
            state.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            state.serviceChargeApplied
        }
        val serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val finalOrder = targetOrder.copy(
            carts = updatedOrder,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        // Ensure at least one course exists before assigning cart items
        val stateWithCourse = state

        setState {

            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = stateWithCourse.tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                // Table-specific sync
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.cartItemsWithCourses[currentTableId]?.associateBy { it.cart.uuid }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.uuid] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: stateWithCourse.getCurrentTableSelectedCourseForNewItems().ifEmpty {
                                stateWithCourse.getCurrentTableAvailableCourses()
                                    .firstOrNull()?.name
                                    ?: ""
                            },
                    )
                } ?: emptyList()

                val updatedCartItemsWithCourses =
                    stateWithCourse.cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                stateWithCourse.copy(
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem,
                        optionSets = optionDetails.optionSets ?: mutableListOf(),
                        updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )

            } else {

                // Global sync when no tables
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = stateWithCourse.getCurrentTableSelectedCourseForNewItems()
                            .ifEmpty {
                                stateWithCourse.getCurrentTableAvailableCourses()
                                    .firstOrNull()?.name
                                    ?: ""
                            },
                    )
                } ?: emptyList()

                stateWithCourse.copy(
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem,
                        optionSets = optionDetails.optionSets ?: mutableListOf(),
                        updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    globalCartItemsWithCourses = updatedGlobalCartItems
                )
            }
        }

        // Sync course assignments after updating cart stock
        syncCartItemsWithCourses(state = stateWithCourse)
    }

    fun updateCartItemNotes(
        order: Order, cart: Cart, notes: String,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order
        }

        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item
            if (it.uuid == cart.uuid) {
                it.copy(notes = notes)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to global cart
                copy(order = finalOrder)
            }
        }
    }

    fun voidCartItem(
        order: Order, cart: Cart,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        tableOrders: Map<Int, Order>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }
        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item
            if (it.uuid == cart.uuid) {
                // Create a voided version of the cart item with all prices set to 0 (keep quantity unchanged)
                val voidedStoreItem = it.storeItem?.copy(
                    price = 0.0,
                    billAmount = 0.0,
                    tax = 0.0,
                    discountedAmount = 0.0,
                    optionSets = it.storeItem.optionSets?.map { optionSet ->
                        optionSet.copy(
                            options = optionSet.options.map { option ->
                                option?.copy(price = 0.0)
                            } ?: mutableListOf()
                        )
                    }
                )

                val notes = if (it.notes?.contains("VOIDED") == true) {
                    it.notes
                } else {
                    (it.notes ?: "") + if (it.notes.isNullOrEmpty()) "VOIDED" else " - VOIDED"
                }

                it.copy(
                    storeItem = voidedStoreItem,
                    price = 0.0,
                    netPayable = 0.0,
                    tax = 0.0,
                    extraPrice = 0.0,
                    optionPrice = 0.0,
                    discount = 0.0,
                    notes = notes
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumOf { it.tax ?: 0.0 } ?: 0.0

        setState {
            // Check if service charge is applied and include it in total
            val isServiceChargeApplied = if (currentTableId != null) {
                tableServiceChargeApplied[currentTableId] ?: false
            } else {
                serviceChargeApplied
            }
            val serviceChargeAmount = if (isServiceChargeApplied) {
                netPayable * (getServiceChargePercentage() / 100.0)
            } else {
                0.0
            }
            val totalPrice = netPayable + totalTax + serviceChargeAmount
            if (currentTableId != null) {
                val finalOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                val currentOrder = finalOrder
                val existingAssignments =
                    cartItemsWithCourses[currentTableId]?.associateBy { it.cart.storeItem?.id }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: if (getCurrentTableSelectedCourseForNewItems().isNotEmpty()) getCurrentTableSelectedCourseForNewItems() else getCurrentTableAvailableCourses().firstOrNull()?.name
                                ?: "",
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                copy(
                    tableOrders = updatedTableOrders,
                    order = order.copy(
                        carts = updatedOrder,
                        netPayable = netPayable,
                        tax = totalTax,
                        totalPrice = totalPrice
                    ),
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )
            } else {

                val currentOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                val existingAssignments =
                    globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: if (getCurrentTableSelectedCourseForNewItems().isNotEmpty()) getCurrentTableSelectedCourseForNewItems() else getCurrentTableAvailableCourses().firstOrNull()?.name
                                ?: "",
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                // Fallback to global cart
                copy(
                    order = currentOrder,
                    globalCartItemsWithCourses = updatedGlobalCartItems,
                )
            }
        }
    }

    /**
     * Save table order when first item is added to cart
     */
    private fun saveTableOrderIfNeeded(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ) {
        val callSaveTable =
            throttleLatest<Unit>(coroutineScope = viewModelScope, intervalMs = 1000) {
                if (tableJob?.isActive == true) {
                    tableJob?.cancel()
                }
                tableJob = viewModelScope.launch(Dispatchers.IO) {
                    createOrUpdateOrderForTableUseCase(
                        tableId = tableId,
                        order = order,
                        state = state
                    ).execute {
                        when (it) {
                            is Success -> {
//                            val data = it()()?.data
//                            if(data != null) {
//                                loadExistingOrderToTable(
//                                    tableId = tableId,
//                                    orderData = data,
//                                    state = state
//                                )
//                            }
                                copy()
                            }

                            else -> {
                                copy()
                            }
                        }
                    }
                }

            }
        callSaveTable(Unit)
    }

    fun addItemToCart(
        order: Order, storeItem: StoreItem, optionDetails: OptionDetails, quantity: Int,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        state: ProductsScreenState
    ) {
        val currentTableId = state.getCurrentTableId()
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()

        // Auto-create and select course if no courses exist
        var updatedState = state
        val availableCourses = state.getCurrentTableAvailableCourses()
        if (availableCourses.isEmpty()) {
            // Create first course and auto-select it
            updatedState = createAndSelectFirstCourse(state)
        }

        // Get the current selected course for new items (table-specific or global)
        val currentCourseId = updatedState.getCurrentTableSelectedCourseForNewItems().ifEmpty {
            updatedState.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
        }

        val updatedStoreItem = storeItem.copy(
            optionSets = optionDetails.optionSets ?: mutableListOf(),
            quantity = quantity,
            billAmount = calculateTotal(
                stockItem = storeItem,
                optionSets = optionDetails.optionSets ?: mutableListOf(),
                updatedStock = quantity
            ).billAmount ?: 0.0,
            courseId = currentCourseId // Add course ID to store item
        )

        // Check if item with same ID, options, and course already exists in cart
        // Exclude items that have been sent to kitchen (they should be treated as locked)
        val existingCartItemIndex = listCart.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == storeItem.id &&
                    areOptionSetsEqual(cartItem.storeItem?.optionSets, optionDetails.optionSets) &&
                    cartItem.storeItem?.courseId == currentCourseId &&
                    !cartItem.sentToKitchen // Don't merge with items already sent to kitchen
        }

        if (existingCartItemIndex != -1) {
            // Item already exists, increment its quantity
            val existingCartItem = listCart[existingCartItemIndex]
            val newQuantity = (existingCartItem.quantity ?: 0) + quantity

            // Recalculate totals for the updated quantity
            val updatedStoreItemForExisting = existingCartItem.storeItem?.copy(
                quantity = newQuantity,
                billAmount = calculateTotal(
                    stockItem = existingCartItem.storeItem,
                    optionSets = optionDetails.optionSets ?: mutableListOf(),
                    updatedStock = newQuantity
                ).billAmount ?: 0.0
            )

            val itemPrice =
                updatedStoreItemForExisting?.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItemForExisting?.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItemForExisting?.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable =
                updatedStoreItemForExisting?.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            // Update the existing cart item
            listCart[existingCartItemIndex] = existingCartItem.copy(
                storeItem = updatedStoreItemForExisting,
                quantity = newQuantity,
                price = itemPrice,
                tax = itemTax,
                discount = itemDiscount,
                netPayable = itemNetPayable
            )
        } else {
            // Item doesn't exist, add new cart item
            val itemPrice = updatedStoreItem.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItem.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItem.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable = updatedStoreItem.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            listCart.add(
                Cart(
                    storeItem = updatedStoreItem,
                    quantity = quantity,
                    price = itemPrice,
                    extraPrice = 0.0,
                    tax = itemTax,
                    discount = itemDiscount,
                    netPayable = itemNetPayable,
                    optionPrice = 0.0,
                    isB1G1 = false
                )
            )
        }

        // Calculate order-level totals
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }

        // Check if service charge is applied and include it in total
        val serviceChargeApplied = if (currentTableId != null) {
            state.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            state.serviceChargeApplied
        }
        val serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        if (currentTableId != null) {
            // Update table-specific cart
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            // Auto-apply service charge if this is the first item and conditions are met
            val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
            val wasCartEmpty = (targetOrder.carts?.isEmpty() ?: true)
            val shouldAutoApply = shouldAutoApplyServiceCharge()
            val wasManuallyRemoved =
                state.tableServiceChargeManuallyRemoved[currentTableId] ?: false
            val isServiceChargeAlreadyApplied =
                state.tableServiceChargeApplied[currentTableId] ?: false

            if (wasCartEmpty && shouldAutoApply && !wasManuallyRemoved && !isServiceChargeAlreadyApplied) {
                updatedTableServiceCharge[currentTableId] = true
                // Recalculate total with service charge
                val serviceChargeAmount = netPayable * (getServiceChargePercentage() / 100.0)
                val finalTotalWithServiceCharge = netPayable + totalTax + serviceChargeAmount
                updatedTableOrders[currentTableId] =
                    updatedOrder.copy(totalPrice = finalTotalWithServiceCharge)
            }

            // Course was created, merge the states
            updatedState = updatedState.copy(
                tableOrders = updatedTableOrders,
                tableServiceChargeApplied = updatedTableServiceCharge,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )

            setState {
                // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                updatedState.copy(
                    tableOrders = updatedTableOrders,
                    tableServiceChargeApplied = updatedTableServiceCharge,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        } else {
            // Fallback to global cart
            val wasCartEmpty = (targetOrder.carts?.isEmpty() ?: true)
            val shouldAutoApply = shouldAutoApplyServiceCharge()
            val isServiceChargeAlreadyApplied = serviceChargeApplied

            if (wasCartEmpty && shouldAutoApply && !isServiceChargeAlreadyApplied) {
                // Auto-apply service charge for global cart
                val serviceChargeAmount = netPayable * (getServiceChargePercentage() / 100.0)
                val finalTotalWithServiceCharge = netPayable + totalTax + serviceChargeAmount
                val updatedOrderWithServiceCharge =
                    updatedOrder.copy(totalPrice = finalTotalWithServiceCharge)

                updatedState = updatedState.copy(
                    order = updatedOrderWithServiceCharge,
                    serviceChargeApplied = true,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
                setState {
                    // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                    updatedState.copy(
                        order = updatedOrderWithServiceCharge,
                        serviceChargeApplied = true,
                        showCart = updatedOrder.carts?.isNotEmpty() ?: false
                    )
                }
            } else {
                updatedState = updatedState.copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
                setState {
                    // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                    updatedState.copy(
                        order = updatedOrder,
                        showCart = updatedOrder.carts?.isNotEmpty() ?: false
                    )
                }
            }
        }

        // Sync course assignments after adding item
        updatedState = syncCartItemsWithCourses(state = updatedState)

        // Save/update table order if needed
        if (currentTableId != null) {
            viewModelScope.launch {
                saveTableOrderIfNeeded(currentTableId, updatedOrder, state = updatedState)
            }
        }
    }

    fun updateItemInCart(
        order: Order, storeItem: StoreItem, cart: Cart, quantity: Int,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        state: ProductsScreenState
    ) {

        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()

        // Auto-create and select course if no courses exist
        var updatedState = state
        val availableCourses = state.getCurrentTableAvailableCourses()
        if (availableCourses.isEmpty()) {
            // Create first course and auto-select it
            updatedState = createAndSelectFirstCourse(state)
        }

        // Get the current selected course for new items (table-specific or global)
        val currentCourseId = updatedState.getCurrentTableSelectedCourseForNewItems().ifEmpty {
            updatedState.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
        }

        val updatedStoreItem = storeItem.copy(
            optionSets = cart.storeItem?.optionSets ?: mutableListOf(),
            quantity = quantity,
            billAmount = calculateTotal(
                stockItem = storeItem,
                optionSets = cart.storeItem?.optionSets ?: mutableListOf(),
                updatedStock = quantity
            ).billAmount ?: 0.0,
            courseId = currentCourseId // Add course ID to store item
        )

        // Find and update the existing cart item
        val existingCartItemIndex = listCart.indexOfFirst { item ->
            item.uuid == cart.uuid
        }

        if (existingCartItemIndex != -1) {
            // Update existing cart item
            val existingCartItem = listCart[existingCartItemIndex]

            // Recalculate totals for the updated quantity
            val itemPrice = updatedStoreItem.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItem.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItem.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable = updatedStoreItem.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            listCart[existingCartItemIndex] = existingCartItem.copy(
                storeItem = updatedStoreItem,
                quantity = quantity,
                price = itemPrice,
                tax = itemTax,
                discount = itemDiscount,
                netPayable = itemNetPayable
            )
        }

        // Calculate order-level totals
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }

        // Check if service charge is applied and include it in total
        val serviceChargeApplied = if (currentTableId != null) {
            state.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            state.serviceChargeApplied
        }
        val serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        if (currentTableId != null) {
            // Update table-specific cart
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            updatedState = updatedState.copy(
                tableOrders = updatedTableOrders,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )

            setState {
                updatedState.copy(
                    tableOrders = updatedTableOrders,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        } else {
            updatedState = updatedState.copy(
                order = updatedOrder,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )
            setState {
                updatedState.copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        }

        // Sync course assignments after updating item
        updatedState = syncCartItemsWithCourses(state = updatedState)

        // Save/update table order if needed
        if (currentTableId != null) {
            viewModelScope.launch {
                saveTableOrderIfNeeded(currentTableId, updatedOrder, state = updatedState)
            }
        }

    }


    /**
     * Create and auto-select the first course when no courses exist
     */
    private fun createAndSelectFirstCourse(state: ProductsScreenState): ProductsScreenState {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )

        return if (currentTableId != null) {
            // Handle table-specific courses
            val courseName = ""
            val newCourse = MealCourse(name = courseName, sortOrder = 0)
            val updatedCourses = listOf(newCourse)

            // Update table-specific courses
            val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
            updatedTableCourses[currentTableId] = updatedCourses

            // Auto-select the new course for new items (use course name as ID for consistency)
            val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
            updatedTableSelectedCourses[currentTableId] = courseName

            // Set as active course
            val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
            updatedTableActiveCourses[currentTableId] = courseName

            // Set first course to Go status
            val updatedTableCourseStatuses = state.tableCourseStatuses.toMutableMap()
            val tableStatuses =
                updatedTableCourseStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
            tableStatuses[courseName] = CourseStatus.GO
            updatedTableCourseStatuses[currentTableId] = tableStatuses

            // Update course status queue - add to go queue
            val updatedTableCourseStatusQueues = state.tableCourseStatusQueues.toMutableMap()
            val currentQueue = updatedTableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
            updatedTableCourseStatusQueues[currentTableId] = currentQueue.addToGoQueue(courseName)

            state.copy(
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses,
                tableCourseStatuses = updatedTableCourseStatuses,
                tableCourseStatusQueues = updatedTableCourseStatusQueues
            )
        } else {
            // Handle global courses
            val courseName = ""
            val newCourse = MealCourse(name = courseName, sortOrder = 0)
            val updatedCourses = listOf(newCourse)

            // Set first course to Go status
            val updatedCourseStatuses = state.courseStatuses.toMutableMap()
            updatedCourseStatuses[courseName] = CourseStatus.GO

            // Update global course status queue - add to go queue
            val updatedCourseStatusQueue = state.getStatusQueue().addToGoQueue(courseName)

            state.copy(
                availableCourses = updatedCourses,
                selectedCourseForNewItems = courseName,
                currentActiveCourse = courseName,
                courseStatuses = updatedCourseStatuses,
                courseStatusQueue = updatedCourseStatusQueue
            )
        }
    }

    /**
     * Helper function to compare two option sets for equality
     * Used to determine if an item with the same options already exists in cart
     */
    private fun areOptionSetsEqual(
        optionSets1: List<OptionSet>?,
        optionSets2: List<OptionSet>?
    ): Boolean {
        if (optionSets1 == null && optionSets2 == null) return true
        if (optionSets1 == null || optionSets2 == null) return false
        if (optionSets1.size != optionSets2.size) return false

        // Sort both lists by ID for consistent comparison
        val sorted1 = optionSets1.sortedBy { it.id }
        val sorted2 = optionSets2.sortedBy { it.id }

        return sorted1.zip(sorted2).all { (set1, set2) ->
            set1.id == set2.id && areOptionsEqual(set1.options, set2.options)
        }
    }

    /**
     * Helper function to compare two option lists for equality
     */
    private fun areOptionsEqual(
        options1: List<Option?>?,
        options2: List<Option?>?
    ): Boolean {
        if (options1 == null && options2 == null) return true
        if (options1 == null || options2 == null) return false
        if (options1.size != options2.size) return false

        // Sort both lists by ID for consistent comparison
        val sorted1 = options1.filterNotNull().sortedBy { it.id }
        val sorted2 = options2.filterNotNull().sortedBy { it.id }

        return sorted1.zip(sorted2).all { (option1, option2) ->
            option1.id == option2.id &&
                    option1.optionchecked == option2.optionchecked &&
                    option1.quantity == option2.quantity
        }
    }

    /**
     * Extension function to convert StoreItem to StockItem
     * Used for calculations when updating existing cart items
     */
    fun StoreItem.toStockItem(): StockItem {
        return StockItem(
            additionalInfo = this.additionalInfo,
            billAmount = this.billAmount,
            brandId = this.brandId,
            businessId = this.businessId,
            categoryId = this.categoryId,
            createdBy = this.createdBy,
            createdOn = this.createdOn,
            dailyCapacity = this.dailyCapacity,
            description = this.description,
            discountType = this.discountType,
            discountedAmount = this.discountedAmount?.toString(),
            id = this.id,
            ingredients = this.ingredients,
            modifiedBy = this.modifiedBy,
            modifiedOn = this.modifiedOn,
            name = this.name,
            pic = this.pic,
            preparationTime = this.preparationTime,
            price = this.price,
            servingSize = this.servingSize,
            stock = this.quantity,
            storeId = this.storeId,
            tax = this.tax,
            unitId = this.unitId,
            vat = this.vat
        )
    }

    fun removeItemFromCart(
        order: Order,
        cartItem: Cart,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        currentState: ProductsScreenState
    ): StateFlow<Async<Order>> {
        val flow = MutableStateFlow<Async<Order>>(Loading())
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()

        // Remove the specific cart item using UUID for unique identification
        listCart.removeAll { it.uuid == cartItem.uuid }

        // Recalculate totals after removal
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }

        // Get current state to check service charge
        var serviceChargeApplied = false
        var serviceChargeAmount = 0.0

        serviceChargeApplied = if (currentTableId != null) {
            currentState.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            currentState.serviceChargeApplied
        }
        serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        var updatedState: ProductsScreenState = currentState

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = updatedOrder
                updatedState = currentState.copy(
                    tableOrders = updatedTableOrders,
                    showCart = selectedTables.isNotEmpty()
                )
                copy(
                    tableOrders = updatedTableOrders,
                    showCart = selectedTables.isNotEmpty() // Show cart if tables are selected, even if empty
                )
            } else {

                updatedState = currentState.copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
                // Fallback to global cart
                copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        }

        // Sync course assignments after removing item
        syncCartItemsWithCourses(state = updatedState)

        // Save/update table order if needed
        if (currentTableId != null) {
            viewModelScope.launch {
                saveTableOrderIfNeeded(currentTableId, updatedOrder, state = updatedState)
            }
        }

        // Check if cart is now empty and trigger complete reset if needed
        checkAndResetIfCartEmpty(updatedOrder, currentTableId, state = updatedState)

        flow.value = Success(updatedOrder)
        return flow
    }

    /**
     * Simplified removeItemFromCart method that uses current state
     */
    fun removeItemFromCart(cartItem: Cart, state: ProductsScreenState): StateFlow<Async<Order>> {
        val currentState = state
        return removeItemFromCart(
            order = currentState.order,
            cartItem = cartItem,
            tableOrders = currentState.tableOrders,
            selectedTableIndex = currentState.selectedTableIndex,
            selectedTables = currentState.selectedTables,
            currentState = state
        )
    }

    fun updateProductDetailsBottomSheetVisibility(cart: com.thedasagroup.suminative.data.model.request.order.Cart?) {
        setState {
            copy(isBottomSheetVisible = cart)
        }
    }

    /**
     * Open product details prefilled from an existing cart item
     */
//    fun openProductDetailsFromCart(cart: Cart) {
//        val st = cart.storeItem ?: return
//        // Save prefill data in state
//        setState {
//            copy(prefillFromCart = st, prefillQuantity = cart.quantity ?: 1, prefillCart = cart)
//        }
//        // Open bottom sheet with this product
//        updateProductDetailsBottomSheetVisibility(cart)
//    }
//
//    /**
//     * Clear any prefill state (call on dismiss / after add)
//     */
//    fun clearPrefillFromCart() {
//        setState { copy(prefillFromCart = null, prefillQuantity = 1, prefillCart = null) }
//    }

    /**
     * Apply option selections and quantity from a cart's StoreItem to the loaded OptionDetails
     */
    fun applyPrefillOptions(
        prefillStoreItem: StoreItem,
        optionDetails: OptionDetails,
        quantity: Int,
        state: ProductsScreenState
    ): ProductsScreenState {
        val updatedOptionSets = optionDetails.optionSets?.map { odSet ->
            val matchingPrefillSet = prefillStoreItem.optionSets?.find { it.id == odSet.id }
            if (matchingPrefillSet != null) {
                val updatedOptions = odSet.options.map { opt ->
                    val prefillOpt = matchingPrefillSet.options.find { it?.id == opt?.id }
                    if (prefillOpt != null) {
                        opt?.copy(
                            optionchecked = (prefillOpt.quantity ?: 0) > 0,
                            quantity = prefillOpt.quantity ?: 0
                        )
                    } else opt
                }
                odSet.copy(options = updatedOptions)
            } else odSet
        } ?: optionDetails.optionSets

        val updatedOptionDetails = optionDetails.copy(optionSets = updatedOptionSets ?: emptyList())

        val updatedState = state.copy(
            optionDetailsResponse = Success(updatedOptionDetails),
            stock = quantity,
            productTotal = calculateTotal(
                prefillStoreItem,
                updatedOptionDetails.optionSets ?: mutableListOf(),
                updatedStock = quantity
            ).billAmount ?: 0.0
        )

        // Update state with new selections and quantity
        setState {
            copy(
                optionDetailsResponse = Success(updatedOptionDetails),
                stock = quantity,
                productTotal = calculateTotal(
                    prefillStoreItem,
                    updatedOptionDetails.optionSets ?: mutableListOf(),
                    updatedStock = quantity
                ).billAmount ?: 0.0
            )
        }

        return updatedState
    }


    suspend fun placeOrder(
        order: Order,
        transId: String,
        state: ProductsScreenState
    ): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }

        val updatedOrder = order.copy(
            serviceChargePercentage = prefs.storeConfigurations?.data?.serviceChargePercentage,
            isServiceChargeApplied = state.isServiceChargeApplied()
        )

        // Get current customer and add to order
        val currentCustomer = state.getCurrentCustomer()
        val orderWithCustomer = if (currentCustomer != null) {
            updatedOrder.copy(
                customerId = currentCustomer.id
            )
        } else {
            updatedOrder
        }

        orderUseCase(orderWithCustomer, transId = transId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        orderSyncManager.triggerImmediateSync()
                    }
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun getOptionDetails(
        itemId: Int, stockItem: StockItem,
        state: ProductsScreenState
    ): StateFlow<Async<OptionDetails>> {
        val flow = MutableStateFlow<Async<OptionDetails>>(Loading())
        setState {
            copy(optionDetailsResponse = Loading())
        }
        getOptionDetailsUseCase(itemId = itemId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    val updatedState = applyPrefillOptions(
                        prefillStoreItem = state.appliedPrefill?.storeItem
                            ?: stockItem.toStoreItem(),
                        optionDetails = it()() ?: OptionDetails(),
                        quantity = state.appliedPrefill?.storeItem?.quantity ?: stockItem.stock
                        ?: 1,
                        state = state
                    )
                    copy(
                        optionDetailsResponse = updatedState.optionDetailsResponse,
                        productTotal = calculateTotal(
                            stockItem = updatedState.appliedPrefill?.storeItem ?: StoreItem(),
                            optionSets = updatedState.optionDetailsResponse()?.optionSets
                                ?: mutableListOf(),
                            updatedStock = updatedState.stock
                        ).billAmount ?: 0.0,
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(optionDetailsResponse = Uninitialized)
                }
            }
        }
        return flow
    }


    fun updateSelectedOptionCondition1(
        option: Option,
        stock: Int,
        stockItem: StoreItem,
        optionDetails: OptionDetails,
        currentOptionSet: OptionSet,
        state: ProductsScreenState
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(
                            optionchecked = true, quantity = 1
                        )
                    } else {
                        currentOption?.copy(optionchecked = false, quantity = 0)
                    }
                }
            } else mutableListOf()
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }
        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        val storeItem2 =
            stockItem.copy(optionSets = updatedOptionDetails.optionSets ?: mutableListOf())
        setState {
            copy(
                appliedPrefill = appliedPrefill?.copy(storeItem = storeItem2),
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem,
                    updatedOptionDetails.optionSets ?: mutableListOf(),
                    updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun addSelectedOptionCondition2(
        optionDetails: OptionDetails,
        currentOptionSet: OptionSet,
        option: Option,
        stockItem: StoreItem,
        stock: Int,
        state: ProductsScreenState
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(
                            optionchecked = true, quantity = 1
                        )
                    } else {
                        currentOption
                    }
                }
            } else mutableListOf()
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }
        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        val storeItem2 =
            stockItem.copy(optionSets = updatedOptionDetails.optionSets ?: mutableListOf())
        setState {
            copy(
                appliedPrefill = appliedPrefill?.copy(storeItem = storeItem2),
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem,
                    updatedOptionDetails.optionSets ?: mutableListOf(),
                    updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun removeSelectedOptionCondition2(
        optionDetails: OptionDetails,
        option: Option,
        currentOptionSet: OptionSet,
        stockItem: StoreItem,
        stock: Int,
        state: ProductsScreenState
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        currentOption?.copy(optionchecked = false, quantity = 0)
                    } else {
                        currentOption
                    }
                }
            } else {
                mutableListOf()
            }
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }

        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        val storeItem2 =
            stockItem.copy(optionSets = updatedOptionDetails.optionSets ?: mutableListOf())
        setState {
            copy(
                appliedPrefill = appliedPrefill?.copy(storeItem = storeItem2),
                optionDetailsResponse = Success(updatedOptionDetails),
                productTotal = calculateTotal(
                    stockItem,
                    updatedOptionDetails.optionSets ?: mutableListOf(),
                    updatedStock = stock
                ).billAmount ?: 0.0,
            )
        }
    }

    fun calculateTotal(
        stockItem: StoreItem, optionSets: List<OptionSet>, updatedStock: Int
    ): StoreItem {
        if (optionSets?.isNotEmpty() == true) {
            val optionPrice = optionSets.flatMap { optionSet ->
                optionSet.options.map { option ->
                    if (option?.optionchecked == true) {
                        (option.price ?: 0.0) * (option.quantity?.toDouble() ?: 0.0)
                    } else {
                        0.0
                    }
                }
            }.sum()
            val price = stockItem.price ?: 0.0
            val tax = stockItem.tax ?: 0.0
            val discount = stockItem.discountedAmount ?: 0.0
            val total = (price + tax - discount + optionPrice) * (updatedStock)
            return stockItem.copy(
                price = price, tax = tax, discountedAmount = discount, billAmount = total
            )
        } else {
            val price = stockItem.price ?: 0.0
            val tax = stockItem.tax ?: 0.0
            val discount = stockItem.discountedAmount ?: 0.0
            val total = (price + tax - discount) * (updatedStock)
            return stockItem.copy(
                price = price, tax = tax, discountedAmount = discount, billAmount = total
            )
        }
    }

    fun updateOptionStock(
        option: Option,
        currentOptionSet: OptionSet,
        stock: Int,
        optionStock: Int,
        optionDetails: OptionDetails,
        stockItem: StoreItem,
    ) {
        val options = optionDetails.optionSets?.flatMap { optionSet ->
            if (optionSet.id == currentOptionSet.id) {
                optionSet.options.map { currentOption ->
                    if (currentOption?.id == option.id) {
                        if (optionStock > 0) {
                            currentOption?.copy(optionchecked = true, quantity = optionStock)
                        } else {
                            currentOption?.copy(optionchecked = false, quantity = optionStock)
                        }
                    } else {
                        currentOption
                    }
                }
            } else {
                mutableListOf()
            }
        }
        val optionSets = optionDetails.optionSets?.map {
            if (it.id == currentOptionSet.id) {
                it.copy(options = options ?: mutableListOf())
            } else {
                it
            }
        }


        val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
        val storeItem2 =
            stockItem.copy(optionSets = updatedOptionDetails.optionSets ?: mutableListOf())

        setState {
            copy(
                appliedPrefill = appliedPrefill?.copy(storeItem = storeItem2),
                optionDetailsResponse = Success(updatedOptionDetails),
                stock = stock,
                productTotal = calculateTotal(
                    stockItem,
                    updatedOptionDetails.optionSets ?: mutableListOf(),
                    updatedStock = stock
                ).billAmount ?: 0.0
            )
        }
    }

    fun updateShowPrintingPreview(order: OrderItem2?, shouldPrintInstant: Boolean = false) {
        setState {
            copy(isShowPrintingPreview = order, shouldPrintInstant = shouldPrintInstant)
        }
    }

    fun showSalesReportDialog(show: Boolean) {
        setState {
            copy(showSalesReportDialog = show)
        }
    }

    fun updateOrder(order: Order) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = order
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to global cart
                copy(order = order)
            }
        }
    }

    fun updateCartVisibility(visible: Boolean) {
        setState {
            if (!visible) {
                if(isMobilePOS){
                    // When closing cart, unselect current table
                    copy(
                        showCart = false,
                    )
                }
                else {
                    // When closing cart, unselect current table
                    copy(
                        showCart = false,
                        selectedTableIndex = -1 // Unselect current table
                    )
                }

            } else {
                copy(showCart = true)
            }
        }
    }

    /**
     * Link a customer from rewards to the current table or global order
     */
    fun linkCustomerToCurrentTable(customer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Link customer to specific table
                val updatedTableCustomers = tableCustomers.toMutableMap()
                if (customer != null) {
                    updatedTableCustomers[currentTableId] = customer
                } else {
                    updatedTableCustomers.remove(currentTableId)
                }
                copy(tableCustomers = updatedTableCustomers)
            } else {
                // Link customer to global order (walk-in)
                copy(selectedCustomer = customer)
            }
        }
    }

    /**
     * Link a customer to a specific table
     */
    fun linkCustomerToTable(
        tableId: Int,
        customer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?
    ) {
        setState {
            val updatedTableCustomers = tableCustomers.toMutableMap()
            if (customer != null) {
                updatedTableCustomers[tableId] = customer
            } else {
                updatedTableCustomers.remove(tableId)
            }
            copy(tableCustomers = updatedTableCustomers)
        }
    }

    /**
     * Clear customer from current table or global order
     */
    fun clearCurrentCustomer() {
        linkCustomerToCurrentTable(null)
    }

    fun clearGlobalCustomer() {
        setState {
            copy(selectedCustomer = null)
        }
    }

    /**
     * Clear customer from specific table
     */
    fun clearTableCustomer(tableId: Int) {
        linkCustomerToTable(tableId, null)
    }

    /**
     * Convert RewardsCustomer to Customer for order creation
     */
    private fun convertRewardsCustomerToCustomer(rewardsCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer): com.thedasagroup.suminative.data.model.response.store_orders.Customer {
        return com.thedasagroup.suminative.data.model.response.store_orders.Customer(
            id = rewardsCustomer.id,
            businessId = rewardsCustomer.businessId,
            name = rewardsCustomer.name,
            email = rewardsCustomer.email,
            phone = rewardsCustomer.phone,
            pic = rewardsCustomer.pic,
            isEmailVerified = if (rewardsCustomer.isEmailVerified == true) 1 else 0,
            isMobileVerified = if (rewardsCustomer.isMobileVerified == true) 1 else 0,
            isSocialUser = rewardsCustomer.isSocialUser,
            type = rewardsCustomer.type
        )
    }

    /**
     * Link a rewards customer to the current table/order
     * This method should be called when a customer is selected from the rewards system
     */
    fun linkRewardsCustomer(rewardsCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?) {
        linkCustomerToCurrentTable(rewardsCustomer)
    }

    /**
     * Check if cart is empty after item removal and trigger complete reset if needed
     */
    private fun checkAndResetIfCartEmpty(
        updatedOrder: Order,
        currentTableId: Int?,
        state: ProductsScreenState
    ) {
        val isEmpty = updatedOrder.carts?.isEmpty() ?: true

        if (isEmpty) {
            clearCartAndRemoveTable(state = state, currentTableId = currentTableId)
        }
    }

    fun clearCart() {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Clear the current table's cart and reset course statuses
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                )

                // Clear table-specific cart items with courses
                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = emptyList()

                // Reset course statuses for this table
                val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
                updatedTableCourseStatuses.remove(currentTableId)

                // Reset active course for this table to first course
                val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                updatedTableActiveCourses[currentTableId] =
                    availableCourses.firstOrNull()?.name ?: ""

                // Reset service charge tracking for this table
                val updatedTableServiceChargeApplied = tableServiceChargeApplied.toMutableMap()
                updatedTableServiceChargeApplied.remove(currentTableId)
                val updatedTableServiceChargeManuallyRemoved =
                    tableServiceChargeManuallyRemoved.toMutableMap()
                updatedTableServiceChargeManuallyRemoved.remove(currentTableId)

                copy(
                    tableOrders = updatedTableOrders,
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableActiveCourses = updatedTableActiveCourses,
                    tableServiceChargeApplied = updatedTableServiceChargeApplied,
                    tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved
                )
            } else {
                // Clear global cart and reset global course statuses
                copy(
                    order = Order(
                        carts = emptyList(),
                        netPayable = 0.0,
                        tax = 0.0,
                        totalPrice = 0.0
                    ),
                    globalCartItemsWithCourses = emptyList(),
                    courseStatuses = emptyMap(),
                    currentActiveCourse = availableCourses.firstOrNull()?.name
                )
            }
        }
    }

    /**
     * Handle payment completion by calling Delete Table Order API and toggleOccupied API
     * This is called when payment is successfully completed for a table
     */
    suspend fun handlePaymentCompletion(tableId: Int) {
        try {
            // First, delete the table order
            deleteOrderForTableUseCase(tableId).execute { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        copy()
                    }

                    else -> {
                        copy()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("ProductScreenViewModel", e.message.toString())
            // Handle error - still try to toggle occupied status
//            toggleTableOccupiedAfterPayment(tableId)
        }
    }

    /**
     * Toggle table occupied status to false with netPayable 0 after payment completion
     */
    private suspend fun toggleTableOccupiedAfterPayment(tableId: Int) {
        try {
            val toggleResult = toggleTableOccupiedUseCase(tableId, 0.0)
            toggleResult.execute { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        // Successfully toggled occupied status to false
                        copy()
                    }

                    else -> {
                        // Handle error
                        copy()
                    }
                }
            }
        } catch (e: Exception) {
            // Handle error
        }
    }

    /**
     * Clear cart and remove table after successful order completion
     * This function handles the complete cleanup after payment success
     * Calls Delete Table Order API and toggleOccupied API
     */
    fun clearCartAndRemoveTable(state: ProductsScreenState, currentTableId: Int?) {
        if (currentTableId != null) {
            val updatedTableCourseStatusQueues = state.tableCourseStatusQueues.toMutableMap()
            updatedTableCourseStatusQueues[currentTableId] = CourseStatusQueue()
            // Call Delete Table Order API and toggle occupied status
            viewModelScope.launch(Dispatchers.IO) {
                handlePaymentCompletion(currentTableId)
            }
            // Remove the current table from selected tables
            val updatedTables = state.selectedTables.filter { it.tableId != currentTableId }

            // Remove all table-specific data for this table
            val updatedTableOrders = state.tableOrders.toMutableMap()
            updatedTableOrders.remove(currentTableId)

            val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
            updatedCartItemsWithCourses.remove(currentTableId)

            val updatedTableCourseStatuses = state.tableCourseStatuses.toMutableMap()
            updatedTableCourseStatuses.remove(currentTableId)

            val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
            updatedTableActiveCourses.remove(currentTableId)

            val updatedSelectedCourseFilter = state.selectedCourseFilter.toMutableMap()
            updatedSelectedCourseFilter.remove(currentTableId)

            val updatedTableServiceChargeApplied = state.tableServiceChargeApplied.toMutableMap()
            updatedTableServiceChargeApplied.remove(currentTableId)

            val updatedTableServiceChargeManuallyRemoved =
                state.tableServiceChargeManuallyRemoved.toMutableMap()
            updatedTableServiceChargeManuallyRemoved.remove(currentTableId)

            // Update selected table index
            val newSelectedIndex = when {
                updatedTables.isEmpty() -> 0
                state.selectedTableIndex >= updatedTables.size -> updatedTables.size - 1
                else -> state.selectedTableIndex
            }
            val tableAvailableCourses = state.tableAvailableCourses.toMutableMap()
            tableAvailableCourses[currentTableId] = mutableListOf()

            updateSelectedCartTab(
                cartTab = CartTab.ORDER,
                state = state,
                currentTableId = currentTableId
            )
            setState {
                copy(
                    tableAvailableCourses = tableAvailableCourses,
                    selectedTables = updatedTables,
                    selectedTableIndex = newSelectedIndex,
                    tableOrders = updatedTableOrders,
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableActiveCourses = updatedTableActiveCourses,
                    selectedCourseFilter = updatedSelectedCourseFilter,
                    tableServiceChargeApplied = updatedTableServiceChargeApplied,
                    tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
                    showCart = updatedTables.isNotEmpty(), // Show cart if there are still tables, hide if no tables
                    tableCourseStatusQueues = updatedTableCourseStatusQueues
                )
            }

        } else {
            updateSelectedCartTab(
                cartTab = CartTab.ORDER,
                state = state,
                currentTableId = currentTableId
            )
            // Clear global cart and reset global course statuses
            setState {
                copy(
                    order = Order(
                        carts = emptyList(),
                        netPayable = 0.0,
                        tax = 0.0,
                        totalPrice = 0.0
                    ),
                    availableCourses = mutableListOf(),
                    globalCartItemsWithCourses = emptyList(),
                    courseStatuses = emptyMap(),
                    currentActiveCourse = availableCourses.firstOrNull()?.name,
                    showCart = false,
                    courseStatusQueue = CourseStatusQueue()
                )
            }
        }
    }

    /**
     * Update the order for a specific table
     */
    private fun updateTableOrder(tableId: Int, updatedOrder: Order) {
        setState {
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[tableId] = updatedOrder
            copy(tableOrders = updatedTableOrders)
        }
    }

    /**
     * Get the current table's order
     */
//    private fun getCurrentTableOrder(): Order {
//        val currentTableId = getCurrentTableId()
//        return if (currentTableId != null) {
//            tableOrders[currentTableId] ?: Order()
//        } else {
//            order // Fallback to global order
//        }
//    }

    /**
     * Update course assignment for a cart item
     */
    fun updateCartItemCourse(cartUuid: String, newCourseId: String) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Table-specific course assignment
                val currentCartItems = cartItemsWithCourses[currentTableId] ?: emptyList()
                val updatedCartItems = currentCartItems.map { item ->
                    if (item.cart.uuid == cartUuid) {
                        // Update both the course assignment and the store item's course ID
                        val updatedStoreItem = item.cart.storeItem?.copy(courseId = newCourseId)
                        val updatedCart = item.cart.copy(storeItem = updatedStoreItem)
                        item.copy(cart = updatedCart, courseId = newCourseId)
                    } else {
                        item
                    }
                }

                // Also update the actual table order
                val currentOrder = tableOrders[currentTableId] ?: Order()
                val updatedCarts = currentOrder.carts?.map { cart ->
                    if (cart.uuid == cartUuid) {
                        val updatedStoreItem = cart.storeItem?.copy(courseId = newCourseId)
                        cart.copy(storeItem = updatedStoreItem)
                    } else {
                        cart
                    }
                } ?: emptyList()
                val updatedOrder = currentOrder.copy(carts = updatedCarts)
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = updatedOrder

                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems
                copy(
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    tableOrders = updatedTableOrders
                )
            } else {
                // Global course assignment when no tables
                val updatedGlobalCartItems = globalCartItemsWithCourses.map { item ->
                    if (item.cart.uuid == cartUuid) {
                        // Update both the course assignment and the store item's course ID
                        val updatedStoreItem = item.cart.storeItem?.copy(courseId = newCourseId)
                        val updatedCart = item.cart.copy(storeItem = updatedStoreItem)
                        item.copy(cart = updatedCart, courseId = newCourseId)
                    } else {
                        item
                    }
                }

                // Also update the global order
                val updatedCarts = order.carts?.map { cart ->
                    if (cart.uuid == cartUuid) {
                        val updatedStoreItem = cart.storeItem?.copy(courseId = newCourseId)
                        cart.copy(storeItem = updatedStoreItem)
                    } else {
                        cart
                    }
                } ?: emptyList()
                val updatedOrder = order.copy(carts = updatedCarts)

                copy(
                    globalCartItemsWithCourses = updatedGlobalCartItems,
                    order = updatedOrder
                )
            }
        }
    }

    /**
     * Update course filter for current table
     */
    fun updateCourseFilter(filter: CourseFilter) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Table-specific filter
                val updatedFilters = selectedCourseFilter.toMutableMap()
                updatedFilters[currentTableId] = filter
                copy(selectedCourseFilter = updatedFilters)
            } else {
                // Global filter when no tables
                copy(globalSelectedCourseFilter = filter)
            }
        }
    }

    /**
     * Sync cart items with course assignments when order changes
     */
    fun syncCartItemsWithCourses(state: ProductsScreenState): ProductsScreenState {
        var updatedState: ProductsScreenState
        val currentTableId = state.getCurrentTableId()
        if (currentTableId != null) {
            // Table-specific sync
            val currentOrder = state.tableOrders[currentTableId] ?: Order()
            // Use cart UUID as key to ensure uniqueness across courses
            val existingAssignments =
                state.cartItemsWithCourses[currentTableId]?.associateBy { it.cart.uuid }
                    ?: emptyMap()

            val updatedCartItems = currentOrder.carts?.map { cart ->
                val course = existingAssignments[cart.uuid] ?: CartItemWithCourse(
                    cart = cart,
                    courseId = cart.storeItem?.courseId
                        ?: if (state.getCurrentTableSelectedCourseForNewItems()
                                .isNotEmpty()
                        ) state.getCurrentTableSelectedCourseForNewItems() else state.getCurrentTableAvailableCourses()
                            .firstOrNull()?.name
                            ?: "",
                )
                course.copy(cart = cart)
            } ?: emptyList()

            val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
            updatedCartItemsWithCourses[currentTableId] = updatedCartItems
            updatedState = state.copy(
                cartItemsWithCourses = updatedCartItemsWithCourses
            )
            setState {
                copy(cartItemsWithCourses = updatedCartItemsWithCourses)
            }
        } else {
            // Global sync when no tables
            val currentOrder = state.order
            // Use cart UUID as key to ensure uniqueness across courses
            val existingAssignments =
                state.globalCartItemsWithCourses.associateBy { it.cart.uuid }

            val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                val course = existingAssignments[cart.uuid] ?: CartItemWithCourse(
                    cart = cart,
                    courseId = cart.storeItem?.courseId
                        ?: if (state.getCurrentTableSelectedCourseForNewItems()
                                .isNotEmpty()
                        ) state.getCurrentTableSelectedCourseForNewItems() else state.getCurrentTableAvailableCourses()
                            .firstOrNull()?.name
                            ?: "",
                )
                course.copy(cart = cart)
            } ?: emptyList()
            updatedState = state.copy(
                globalCartItemsWithCourses = updatedGlobalCartItems
            )
            setState {
                copy(globalCartItemsWithCourses = updatedGlobalCartItems)
            }
        }
        return updatedState
    }


    /**
     * Initialize course assignments and courses for a new table
     */
    fun initializeTableCourses(tableId: Int) {
        setState {
            val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
            val updatedFilters = selectedCourseFilter.toMutableMap()
            val updatedTableCourses = tableAvailableCourses.toMutableMap()
            val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()

            if (!updatedCartItemsWithCourses.containsKey(tableId)) {
                updatedCartItemsWithCourses[tableId] = emptyList()
            }
            if (!updatedFilters.containsKey(tableId)) {
                updatedFilters[tableId] = CourseFilter.ALL
            }

            // Initialize table-specific courses if not already present
            if (!updatedTableCourses.containsKey(tableId)) {
                // Start with default courses for new table
                val defaultCourses = listOf(
                    MealCourse("Starters", sortOrder = 1)
                )
                updatedTableCourses[tableId] = defaultCourses
                updatedTableSelectedCourses[tableId] = defaultCourses.firstOrNull()?.name ?: ""
                updatedTableActiveCourses[tableId] = defaultCourses.firstOrNull()?.name ?: ""

                // Set first course to Go status
                val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
                val tableStatuses =
                    updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()
                val firstCourseName = defaultCourses.firstOrNull()?.name
                if (firstCourseName != null) {
                    tableStatuses[firstCourseName] = CourseStatus.GO
                }
                updatedTableCourseStatuses[tableId] = tableStatuses

                // Update course status queue - add to go queue
                val updatedTableCourseStatusQueues = tableCourseStatusQueues.toMutableMap()
                val currentQueue = updatedTableCourseStatusQueues[tableId] ?: CourseStatusQueue()
                if (firstCourseName != null) {
                    updatedTableCourseStatusQueues[tableId] =
                        currentQueue.addToGoQueue(firstCourseName)
                }

                copy(
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    selectedCourseFilter = updatedFilters,
                    tableAvailableCourses = updatedTableCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableCourseStatusQueues = updatedTableCourseStatusQueues
                )
            }

            copy(
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters,
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses
            )
        }

        // Initialize service charge for the table if auto-apply is enabled
        initializeServiceChargeForTable(tableId)
    }

    /**
     * Get the current table ID
     */
    private fun getCurrentTableId(
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ): Int? {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
            selectedTables[selectedTableIndex].tableId
        } else {
            null
        }
    }

    /**
     * Handle table selection based on occupied status
     * a) occupied==true: Call Get Table Order API and load details
     * b) occupied==false: Call toggleOccupied API and show ordering page
     */
    fun handleTableSelectionWithOccupiedStatus(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        table: com.thedasagroup.suminative.data.model.response.reservations.Table,
        state: ProductsScreenState
    ) {
        viewModelScope.launch {
            if (table.occupied) {
                // Table is occupied - get existing order details
                handleOccupiedTableSelection(selection, table, state = state)
            } else {
                // Table is not occupied - toggle occupied status and show ordering page
                handleUnoccupiedTableSelection(selection, table, state = state)
            }
        }
    }

    /**
     * Handle selection of an occupied table
     * Calls Get Table Order API and loads the existing order details
     */
    private suspend fun handleOccupiedTableSelection(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        table: com.thedasagroup.suminative.data.model.response.reservations.Table,
        state: ProductsScreenState
    ) {
        try {
            getSyncedOrderForTableUseCase(table.id).execute { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()()
                        if (response?.success == true && response.data != null) {
                            // Load the existing order data into the table
                            val updatedState =
                                loadExistingOrderToTable(
                                    selection.tableId,
                                    response.data,
                                    state = state
                                )
                            // Add table and show cart
                            val updateState = addSelectedTable(selection, state = updatedState)
                            copy()
                        } else {
                            // Handle error - still add table but show empty cart
                            addSelectedTable(selection, state = state)
                            copy()
                        }
                    }

                    else -> {
                        // Handle error - still add table but show empty cart
                        addSelectedTable(selection, state = state)
                        copy()
                    }
                }
            }
        } catch (e: Exception) {
            // Handle error - still add table but show empty cart
            addSelectedTable(selection, state = state)
        }
    }

    suspend fun syncTable(tableId: Int, state: ProductsScreenState) {
        getSyncedOrderForTableUseCase(tableId = tableId).execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val response = asyncResult()()
                    if (response?.success == true && response.data != null) {
                        // Load the existing order data into the table
                        val updatedState =
                            loadExistingOrderToTable(
                                tableId,
                                response.data,
                                state = state
                            )
                        copy()
                    } else {
                        // Handle error - still add table but show empty cart
//                        addSelectedTable(selection, state = state)
                        copy()
                    }
                }

                else -> {
                    // Handle error - still add table but show empty cart
//                    addSelectedTable(selection, state = state)
                    copy()
                }
            }
        }
    }

    suspend fun syncTableFromSocket(tableId: Int, state: ProductsScreenState) {
        getSyncedOrderForTableUseCase(tableId = tableId).execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val response = asyncResult()()
                    if (response?.success == true && response.data != null) {
                        // Check if table exists in current state
                        if (state.selectedTables.any { it.tableId == tableId }) {
                            // Table exists, just load the order
                            loadExistingOrderToTable(
                                tableId,
                                response.data,
                                state = state
                            )
                        } else {
                            // Table doesn't exist, get details and add to selection
                            viewModelScope.launch {
                                reservationsRepository.getTableById(tableId).execute { result ->
                                    when (result) {
                                        is Success -> {
                                            val table = result()()
                                            if (table != null) {
                                                val selection =
                                                    AreaTableSelectionHelper.AreaTableSelection(
                                                        areaId = table.areaId,
                                                        areaName = "",
                                                        tableId = table.id,
                                                        tableName = table.tableName,
                                                        tableCapacity = table.seatingCapacity,
                                                        isOccupied = true,
                                                        table = table
                                                    )
                                                val updateState =
                                                    if (state.selectedTables.isEmpty()) {
                                                        addSelectedTable(
                                                            selection,
                                                            state = state
                                                        )
                                                    } else {
                                                        addTableSilent(
                                                            selection,
                                                            state = state
                                                        )
                                                    }
                                                loadExistingOrderToTable(
                                                    tableId,
                                                    response.data,
                                                    state = updateState
                                                )
                                                copy()
                                            }
                                            copy()
                                        }

                                        else -> {
                                            copy()
                                        }
                                    }
                                }
                            }
                        }
                        copy()
                    } else {
                        // Handle error - still add table but show empty cart
//                        addSelectedTable(selection, state = state)
                        copy()
                    }
                }

                else -> {
                    // Handle error - still add table but show empty cart
//                    addSelectedTable(selection, state = state)
                    copy()
                }
            }
        }
    }

    /**
     * Handle selection of an unoccupied table
     * Creates a new sync order for the empty table and shows ordering page
     */
    suspend fun handleUnoccupiedTableSelection(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        table: com.thedasagroup.suminative.data.model.response.reservations.Table,
        state: ProductsScreenState
    ) {
        try {
            // Create a new sync order for the empty table
            val syncRequest = createInitialSyncOrderRequest(table.id, state)
            val result = syncOrderToTableUseCase(syncRequest)
            result.execute { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        // Successfully created new sync order
                        addSelectedTable(selection, state = state)
                        copy()
                    }

                    else -> {
                        // Handle error - still add table
                        addSelectedTable(selection, state = state)
                        copy()
                    }
                }
            }
        } catch (e: Exception) {
            // Handle error - still add table
            addSelectedTable(selection, state = state)
        }
    }

    /**
     * Create initial sync order request for an empty table
     */
    private fun createInitialSyncOrderRequest(
        tableId: Int,
        state: ProductsScreenState
    ): SyncOrderRequest {
        val currentCustomer = state.getCurrentCustomer()
        val queue = state.getStatusQueue()
        return SyncOrderRequest(
            tableId = tableId,
            customerId = currentCustomer?.id ?: 0,
            businessId = prefs.store?.businessId ?: 0,
            netPayable = 0.0,
            orderCourses = emptyList(), // Start with empty order courses
            goQueue = queue.goQueue.joinToString(","),
            preparingQueue = queue.preparingQueue.joinToString(","),
            completeQueue = queue.completedCourses.joinToString(","),
            deviceId = prefs.localDeviceId ?: UUID.randomUUID().toString()
        )
    }

    /**
     * Load existing order data into the selected table
     */
    private fun loadExistingOrderToTable(
        tableId: Int,
        orderData: com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderData,
        state: ProductsScreenState
    ): ProductsScreenState {
        // Convert sync order data to internal Order format
        var carts = mutableListOf<Cart>()
        val cartItemsWithCourses = mutableListOf<CartItemWithCourse>()

        // Create a mapping of course names to course IDs
        val courseNameToIdMap = mutableMapOf<String, String>()
        orderData.orderCourses.sortedBy { it.sortOrder }.forEach { course ->
            val courseId = course.coursesName
            courseNameToIdMap[course.coursesName ?: "Course 1"] = courseId ?: "Course 1"
        }

        orderData.orderCourses.sortedBy { it.sortOrder }.forEach { course ->
            val courseId = courseNameToIdMap[course.coursesName] ?: ""
            val courseStatus = course.courseStatus

            // Parse the cartJson to extract items
            course.getCart().forEach { cart ->
                carts.add(cart)

                // Add cart item with proper course assignment
                cartItemsWithCourses.add(
                    CartItemWithCourse(
                        cart = cart,
                        courseId = courseId,
                    )
                )
            }
        }

        val order = Order(
            carts = carts,
            netPayable = orderData.netPayable,
            customerId = orderData.customerId,
            businessId = orderData.businessId
        )

        val tableQueue = state.tableCourseStatusQueues.toMutableMap()
        val goQueue = orderData.goQueue?.split(",")?.filter { it.isNotEmpty() } ?: emptyList()
        val preparingQueue =
            orderData.preparingQueue?.split(",")?.filter { it.isNotEmpty() } ?: emptyList()
        val completeQueue =
            orderData.completeQueue?.split(",")?.filter { it.isNotEmpty() } ?: emptyList()

        val courseStatusQueue = CourseStatusQueue(
            goQueue = goQueue,
            preparingQueue = preparingQueue,
            completedCourses = completeQueue
        )

        tableQueue[tableId] = courseStatusQueue

        // Update table orders
        val updatedTableOrders = state.tableOrders.toMutableMap()
        updatedTableOrders[tableId] = order

        // Update cart items with courses
        val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
        updatedCartItemsWithCourses[tableId] = cartItemsWithCourses

        // Load table-specific available courses from the existing order data
        val tableSpecificCourses = extractCoursesFromOrderData(orderData)
        val updatedTableAvailableCourses = state.tableAvailableCourses.toMutableMap()
        updatedTableAvailableCourses[tableId] = tableSpecificCourses

        val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
        updatedTableSelectedCourses[tableId] = tableSpecificCourses.firstOrNull()?.name ?: ""


        // Mark the first course as Go status
        val updatedTableCourseStatuses = state.tableCourseStatuses.toMutableMap()
        val currentTableStatuses =
            updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()

        val firstCourse = tableSpecificCourses.firstOrNull()
        if (firstCourse != null) {
            // Set first course to Go status
            currentTableStatuses[firstCourse.name] = CourseStatus.GO
        }
        updatedTableCourseStatuses[tableId] = currentTableStatuses

        // Set first course as active course
        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
        if (firstCourse != null) {
            updatedTableActiveCourses[tableId] = firstCourse.name
        }

        setState {
            copy(
                tableCourseStatusQueues = tableQueue,
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                tableAvailableCourses = updatedTableAvailableCourses,
                tableCourseStatuses = updatedTableCourseStatuses,
                tableActiveCourses = updatedTableActiveCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses
            )
        }

        val updatedState = calculateTotalFromOrder(state = state)

        return updatedState.copy(
            tableCourseStatusQueues = tableQueue,
            tableOrders = updatedTableOrders,
            cartItemsWithCourses = updatedCartItemsWithCourses,
            tableAvailableCourses = updatedTableAvailableCourses,
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses,
            tableSelectedCourseForNewItems = updatedTableSelectedCourses
        )
    }

    /**
     * Extract available courses from existing order data
     * Creates MealCourse objects based on the course names in the order
     */
    private fun extractCoursesFromOrderData(
        orderData: com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderData
    ): List<MealCourse> {
        val extractedCourses = mutableListOf<MealCourse>()

        // Extract unique course names from the order data
        val courseNames = orderData.orderCourses.distinct()

        courseNames.forEachIndexed { index, course ->
            val courseName = course.coursesName
            val sortOrder = course.sortOrder
            val mealCourse = MealCourse(
                name = courseName ?: "Starters",
                sortOrder = sortOrder
            )
            extractedCourses.add(mealCourse)
        }

//        // If no courses found in order data, add default courses
//        if (extractedCourses.isEmpty()) {
//            extractedCourses.addAll(
//                listOf(
//                    MealCourse("course_starters", "Starters", "Starters"),
//                    MealCourse("course_mains", "Mains", "Mains"),
//                    MealCourse("course_desserts", "Desserts", "Desserts")
//                )
//            )
//        }

        return extractedCourses
    }

    /**
     * Add a selected table to the list
     */
    fun addSelectedTable(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        state: ProductsScreenState,
    ): ProductsScreenState {
        var updateState = state
        val updatedTables = state.selectedTables.toMutableList()
        // Check if table is already selected
        if (!updatedTables.any { it.tableId == selection.tableId }) {
            updatedTables.add(selection)
            // Initialize empty cart for new table if it doesn't exist
            val updatedTableOrders = state.tableOrders.toMutableMap()
            if (!updatedTableOrders.containsKey(selection.tableId)) {
                updatedTableOrders[selection.tableId] = Order()
            }

            // Initialize course state for new table
            val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
            val updatedFilters = state.selectedCourseFilter.toMutableMap()
            if (!updatedCartItemsWithCourses.containsKey(selection.tableId)) {
                updatedCartItemsWithCourses[selection.tableId] = emptyList()
            }
            if (!updatedFilters.containsKey(selection.tableId)) {
                updatedFilters[selection.tableId] = CourseFilter.ALL
            }

            // Initialize table courses if not already present (but don't auto-create courses yet)
            // Auto-course creation will happen when first item is added
            val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
            val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
            val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

            if (!updatedTableCourses.containsKey(selection.tableId)) {
                updatedTableCourses[selection.tableId] = emptyList() // Start with no courses
                updatedTableSelectedCourses[selection.tableId] = ""
                updatedTableActiveCourses[selection.tableId] = ""
            }

            updateState = state.copy(
                selectedTables = updatedTables,
                selectedTableIndex = updatedTables.size - 1, // Select the newly added table
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters,
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses,
                showCart = true // Always show cart when table is added, even if empty
            )

            setState {
                copy(
                    selectedTables = updatedTables,
                    selectedTableIndex = updatedTables.size - 1, // Select the newly added table
                    tableOrders = updatedTableOrders,
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    selectedCourseFilter = updatedFilters,
                    tableAvailableCourses = updatedTableCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                    showCart = true // Always show cart when table is added, even if empty
                )

            }
        } else {
            // If table already exists, just select it
            val existingIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId }
            setState {
                copy(
                    selectedTableIndex = existingIndex,
                    showCart = true // Always show cart when table is selected, even if empty
                )
            }

            updateState = state.copy(
                selectedTableIndex = existingIndex,
                showCart = true // Always show cart when table is selected, even if empty
            )
        }
        return updateState
    }


    fun addTableSilent(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        state: ProductsScreenState,
    ): ProductsScreenState {
        var updateState = state
        val updatedTables = state.selectedTables.toMutableList()
        // Check if table is already selected
        if (!updatedTables.any { it.tableId == selection.tableId }) {
            updatedTables.add(selection)
            // Initialize empty cart for new table if it doesn't exist
            val updatedTableOrders = state.tableOrders.toMutableMap()
            if (!updatedTableOrders.containsKey(selection.tableId)) {
                updatedTableOrders[selection.tableId] = Order()
            }

            // Initialize course state for new table
            val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
            val updatedFilters = state.selectedCourseFilter.toMutableMap()
            if (!updatedCartItemsWithCourses.containsKey(selection.tableId)) {
                updatedCartItemsWithCourses[selection.tableId] = emptyList()
            }
            if (!updatedFilters.containsKey(selection.tableId)) {
                updatedFilters[selection.tableId] = CourseFilter.ALL
            }

            // Initialize table courses if not already present (but don't auto-create courses yet)
            // Auto-course creation will happen when first item is added
            val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
            val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
            val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

            if (!updatedTableCourses.containsKey(selection.tableId)) {
                updatedTableCourses[selection.tableId] = emptyList() // Start with no courses
                updatedTableSelectedCourses[selection.tableId] = ""
                updatedTableActiveCourses[selection.tableId] = ""
            }

            updateState = state.copy(
                selectedTables = updatedTables,
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters,
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses,
            )

            setState {
                copy(
                    selectedTables = updatedTables,
                    tableOrders = updatedTableOrders,
                    cartItemsWithCourses = updatedCartItemsWithCourses,
                    selectedCourseFilter = updatedFilters,
                    tableAvailableCourses = updatedTableCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                )

            }
        } else {
            // If table already exists, just select it
            val existingIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId }
            setState {
                copy(

                )
            }

            updateState = state.copy(

            )
        }
        return updateState
    }

    /**
     * Remove a selected table from the list
     */
//    fun removeSelectedTable(tableId: Int) {
//        // Clean up course statuses for the removed table
//        onTableRemoved(tableId)
//
//        setState {
//            val updatedTables = selectedTables.filter { it.tableId != tableId }
//            val updatedTableOrders = tableOrders.toMutableMap()
//            // Remove the table's cart data
//            updatedTableOrders.remove(tableId)
//
//            // Remove table-specific customer
//            val updatedTableCustomers = tableCustomers.toMutableMap()
//            updatedTableCustomers.remove(tableId)
//
//            // Remove table-specific cart items with courses
//            val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
//            updatedCartItemsWithCourses.remove(tableId)
//
//            // Remove table-specific course filter
//            val updatedSelectedCourseFilter = selectedCourseFilter.toMutableMap()
//            updatedSelectedCourseFilter.remove(tableId)
//
//            // Remove table-specific service charge tracking
//            val updatedTableServiceChargeApplied = tableServiceChargeApplied.toMutableMap()
//            updatedTableServiceChargeApplied.remove(tableId)
//            val updatedTableServiceChargeManuallyRemoved =
//                tableServiceChargeManuallyRemoved.toMutableMap()
//            updatedTableServiceChargeManuallyRemoved.remove(tableId)
//
//            val newSelectedIndex = when {
//                updatedTables.isEmpty() -> 0
//                selectedTableIndex >= updatedTables.size -> updatedTables.size - 1
//                else -> selectedTableIndex
//            }
//
//            // Update cart visibility based on new selected table
//            val newTableOrder =
//                if (updatedTables.isNotEmpty() && newSelectedIndex < updatedTables.size) {
//                    val newTableId = updatedTables[newSelectedIndex].tableId
//                    updatedTableOrders[newTableId] ?: Order()
//                } else {
//                    Order()
//                }
//
//            copy(
//                selectedTables = updatedTables,
//                selectedTableIndex = newSelectedIndex,
//                tableOrders = updatedTableOrders,
//                tableCustomers = updatedTableCustomers,
//                cartItemsWithCourses = updatedCartItemsWithCourses,
//                selectedCourseFilter = updatedSelectedCourseFilter,
//                tableServiceChargeApplied = updatedTableServiceChargeApplied,
//                tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
//                showCart = newTableOrder.carts?.isNotEmpty() ?: false
//            )
//        }
//    }

    /**
     * Set the selected table index
     */
    fun setSelectedTableIndex(index: Int) {
        setState {
            if (index < selectedTables.size) {
                copy(
                    selectedTableIndex = index,
                    showCart = true // Always show cart when table is selected, even if empty
                )
            } else {
                copy(selectedTableIndex = index)
            }
        }
    }

    /**
     * Get the currently selected table
     */
//    fun getCurrentSelectedTable(): AreaTableSelectionHelper.AreaTableSelection? {
//        return withState(this) { state ->
//            if (state.selectedTables.isNotEmpty() && state.selectedTableIndex < state.selectedTables.size) {
//                state.selectedTables[state.selectedTableIndex]
//            } else null
//        }
//    }

    private fun monitorSyncStatus() {
        viewModelScope.launch {
            orderSyncManager.getLastSyncResult().collect { result ->
                setState {
                    copy(
                        syncStatus = when {
                            result == null -> SyncStatus.Idle
                            result.isSuccess -> SyncStatus.Success(
                                syncedCount = result.syncedCount,
                                totalCount = result.totalCount
                            )

                            else -> SyncStatus.Error(
                                errorMessage = result.errorMessage ?: "Unknown error",
                                failedCount = result.failedCount,
                                totalCount = result.totalCount
                            )
                        }
                    )
                }
            }
        }
    }

    suspend fun placeOrderOffline(order: Order): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }
        offlineOrderUseCase(order).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        orderSyncManager.triggerImmediateSync()
                    }
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun cloudPrint(request: CloudPrintRequest): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        setState {
            copy(orderResponse = Loading())
        }
        cloudPrintUseCase(request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(orderResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(orderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun isMyGuava(): Boolean {
        val storeId = prefs.store?.id
        return prefs.loginResponse?.stores?.firstOrNull { it.id == storeId }?.paymentProcessorName == "My Guava"
    }

    fun isSumUp(): Boolean {
        return false
    }


    fun getCourseCartItems(
        currentOrder: Order,
        existingAssignments: List<CartItemWithCourse>
    ): List<CartItemWithCourse> {
        val updatedCartItems = currentOrder.carts?.map { cart ->
            existingAssignments[cart.storeItem?.id ?: 0]
        } ?: emptyList()

        return updatedCartItems
    }

    /**
     * Add a new course to the available courses list (table-specific or global)
     * Auto-selects the newly added course for new items
     */
    fun addNewCourse(courseName: String, availableCourses: List<MealCourse>) {
        setState {
            val sortOrder = when (courseName) {
                "" -> 0 // Empty course
                "Starters" -> 1
                "Mains" -> 2
                "Desserts" -> 3
                else -> (availableCourses.maxOfOrNull { it.sortOrder }
                    ?: 3) + 1 // Next available sort order
            }
            val newCourse = MealCourse(
                name = courseName,
                sortOrder = sortOrder
            )
            val updatedCourses = availableCourses.toMutableList()
            updatedCourses.add(newCourse)

            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific courses
                val updatedTableCourses = tableAvailableCourses.toMutableMap()
                updatedTableCourses[currentTableId] = updatedCourses

                // Auto-select the newly added course for new items
                val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
                updatedTableSelectedCourses[currentTableId] = courseName

                // If this is the first course for the table, set it to Go status
                val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
                val currentTableStatuses =
                    updatedTableCourseStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()

                if (availableCourses.isEmpty()) {
                    // First course should have Go status
                    currentTableStatuses[courseName] = CourseStatus.GO

                    // Also set as active course
                    val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                    updatedTableActiveCourses[currentTableId] = courseName

                    updatedTableCourseStatuses[currentTableId] = currentTableStatuses

                    // Update course status queue - add to go queue
                    val updatedTableCourseStatusQueues = tableCourseStatusQueues.toMutableMap()
                    val currentQueue =
                        updatedTableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
                    updatedTableCourseStatusQueues[currentTableId] =
                        currentQueue.addToGoQueue(courseName)

                    copy(
                        tableAvailableCourses = updatedTableCourses,
                        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                        tableCourseStatuses = updatedTableCourseStatuses,
                        tableActiveCourses = updatedTableActiveCourses,
                        tableCourseStatusQueues = updatedTableCourseStatusQueues
                    )
                } else {
                    // Add new course to go queue even if it's not the first course
                    val updatedTableCourseStatusQueues = tableCourseStatusQueues.toMutableMap()
                    val currentQueue =
                        updatedTableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
                    updatedTableCourseStatusQueues[currentTableId] =
                        currentQueue.addToGoQueue(courseName)

                    copy(
                        tableAvailableCourses = updatedTableCourses,
                        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                        tableCourseStatusQueues = updatedTableCourseStatusQueues
                    )
                }
            } else {
                // Update global courses and auto-select the new course
                if (availableCourses.isEmpty()) {
                    // First course should have Go status
                    val updatedCourseStatuses = courseStatuses.toMutableMap()
                    updatedCourseStatuses[courseName] = CourseStatus.GO

                    // Update global course status queue - add to go queue
                    val updatedCourseStatusQueue = getStatusQueue().addToGoQueue(courseName)

                    copy(
                        availableCourses = updatedCourses,
                        selectedCourseForNewItems = courseName,
                        currentActiveCourse = courseName,
                        courseStatuses = updatedCourseStatuses,
                        courseStatusQueue = updatedCourseStatusQueue
                    )
                } else {
                    // Add new course to go queue even if it's not the first course
                    val updatedCourseStatusQueue = getStatusQueue().addToGoQueue(courseName)

                    copy(
                        availableCourses = updatedCourses,
                        selectedCourseForNewItems = courseName,
                        courseStatusQueue = updatedCourseStatusQueue
                    )
                }
            }
        }
    }

    /**
     * Add default courses (Starters, Mains, Desserts) to the available courses list
     * Only adds courses that don't already exist (table-specific or global)
     */
    fun addDefaultCourses() {
        setState {
            val defaultCourses = listOf(
                MealCourse("Starters", 1),
                MealCourse("Mains", 2),
                MealCourse("Desserts", 3)
            )

            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Handle table-specific courses
                val currentTableCourses = tableAvailableCourses[currentTableId] ?: emptyList()
                val existingCourseIds = currentTableCourses.map { it.name }.toSet()
                val coursesToAdd = defaultCourses.filter { it.name !in existingCourseIds }

                if (coursesToAdd.isNotEmpty()) {
                    val wasEmpty = currentTableCourses.isEmpty()
                    val updatedCourses = currentTableCourses.toMutableList()
                    updatedCourses.addAll(coursesToAdd)

                    // Update table-specific courses
                    val updatedTableCourses = tableAvailableCourses.toMutableMap()
                    updatedTableCourses[currentTableId] = updatedCourses

                    // If we're adding courses to an empty list, set the first course as active
                    val updatedTableActiveCourses = if (wasEmpty) {
                        tableActiveCourses.toMutableMap().apply {
                            this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                        }
                    } else {
                        tableActiveCourses
                    }

                    // Auto-select the first newly added course for new items
                    val updatedTableSelectedCourses =
                        tableSelectedCourseForNewItems.toMutableMap().apply {
                            this[currentTableId] =
                                coursesToAdd.firstOrNull()?.name
                                    ?: updatedCourses.firstOrNull()?.name
                                            ?: ""
                        }

                    // Update course status queue - add all new courses to go queue
                    val updatedTableCourseStatusQueues = tableCourseStatusQueues.toMutableMap()
                    val currentQueue =
                        updatedTableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
                    var updatedQueue = currentQueue
                    coursesToAdd.forEach { course ->
                        updatedQueue = updatedQueue.addToGoQueue(course.name)
                    }
                    updatedTableCourseStatusQueues[currentTableId] = updatedQueue

                    copy(
                        tableAvailableCourses = updatedTableCourses,
                        tableActiveCourses = updatedTableActiveCourses,
                        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                        tableCourseStatusQueues = updatedTableCourseStatusQueues
                    )
                } else {
                    // No changes needed if all default courses already exist
                    this
                }
            } else {
                // Handle global courses
                val existingCourseIds = availableCourses.map { it.name }.toSet()
                val coursesToAdd = defaultCourses.filter { it.name !in existingCourseIds }

                if (coursesToAdd.isNotEmpty()) {
                    val wasEmpty = availableCourses.isEmpty()
                    val updatedCourses = availableCourses.toMutableList()
                    updatedCourses.addAll(coursesToAdd)

                    val newCurrentActiveCourse = if (wasEmpty) {
                        updatedCourses.firstOrNull()?.name
                    } else {
                        currentActiveCourse
                    }

                    // Auto-select the first newly added course for new items
                    val newSelectedCourse =
                        coursesToAdd.firstOrNull()?.name ?: updatedCourses.firstOrNull()?.name ?: ""

                    // Update global course status queue - add all new courses to go queue
                    var updatedCourseStatusQueue = getStatusQueue()
                    coursesToAdd.forEach { course ->
                        updatedCourseStatusQueue =
                            updatedCourseStatusQueue.addToGoQueue(course.name)
                    }

                    copy(
                        availableCourses = updatedCourses,
                        currentActiveCourse = newCurrentActiveCourse,
                        selectedCourseForNewItems = newSelectedCourse,
                        courseStatusQueue = updatedCourseStatusQueue
                    )
                } else {
                    // No changes needed if all default courses already exist
                    this
                }
            }
        }
    }

    /**
     * Add a new numbered course automatically (Course 1, Course 2, etc.) - table-specific or global
     */
    fun addNumberedCourse(state: ProductsScreenState) {
        val currentTableId = state.getCurrentTableId()

        if (currentTableId != null) {
            // Handle table-specific courses
            val currentTableCourses = state.tableAvailableCourses[currentTableId] ?: emptyList()
            val courseNumber = currentTableCourses.size + 1
            val course = currentTableCourses.size
            val courseName = if (currentTableCourses.firstOrNull { it.name.isEmpty() } != null) {
                when (course) {
                    0 -> ""
                    1 -> "Starters"
                    2 -> "Mains"
                    3 -> "Desserts"
                    else -> "Course $courseNumber"
                }
            } else {
                when (course) {
                    0 -> "Starters"
                    1 -> "Mains"
                    2 -> "Desserts"
                    else -> "Course $courseNumber"
                }
            }
            val sortOrder = when (courseName) {
                "Starters" -> 1
                "Mains" -> 2
                "Desserts" -> 3
                else -> courseNumber + 3 // For courses beyond desserts
            }
            val newCourse = MealCourse(
                name = courseName,
                sortOrder = sortOrder
            )

            val updatedCourses = currentTableCourses.toMutableList()
            updatedCourses.add(newCourse)

            // Update table-specific courses
            val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
            updatedTableCourses[currentTableId] = updatedCourses

            // Auto-select the newly added course for new items
            val updatedTableSelectedCourses =
                state.tableSelectedCourseForNewItems.toMutableMap().apply {
                    this[currentTableId] = courseName // Use course name for consistency
                }

            // Check if previous course has Preparing status and set new course to Go
            // Also set first course (Course 1) to Go status
            val updatedTableCourseStatuses = state.tableCourseStatuses.toMutableMap()
            val currentTableStatuses =
                updatedTableCourseStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()

            var queue = state.getStatusQueue()

            if (courseNumber == 1) {
                // First course should always have Go status
                currentTableStatuses[courseName] = CourseStatus.GO
                queue = queue.addToGoQueue(courseId = courseName)
            } else if (currentTableCourses.isNotEmpty()) {
                val previousCourse = currentTableCourses.last().name
                val previousCourseStatus = currentTableStatuses[previousCourse] ?: CourseStatus.GO
                if (previousCourseStatus == CourseStatus.PREPARING) {
                    // Set new course to Go status
                    currentTableStatuses[courseName] = CourseStatus.GO
                    queue = queue.addToGoQueue(courseId = courseName)
                } else {
                    // Add new course to go queue regardless of previous course status
                    queue = queue.addToGoQueue(courseId = courseName)
                }
            } else {
                // Add new course to go queue for any new course
                queue = queue.addToGoQueue(courseId = courseName)
            }
            updatedTableCourseStatuses[currentTableId] = currentTableStatuses

            val tableCourseQueue = state.tableCourseStatusQueues.toMutableMap()
            tableCourseQueue[currentTableId] = queue

            // If this is the first course, set it as active course
            val updatedTableActiveCourses = if (courseNumber == 1) {
                state.tableActiveCourses.toMutableMap().apply {
                    this[currentTableId] = courseName
                }
            } else {
                // If previous course is Preparing and new course is Go, make new course active
                val previousCourse =
                    if (currentTableCourses.isNotEmpty()) currentTableCourses.last().name else null
                val previousCourseStatus =
                    if (previousCourse != null) currentTableStatuses[previousCourse]
                        ?: CourseStatus.GO else CourseStatus.GO
                if (previousCourseStatus == CourseStatus.PREPARING && currentTableStatuses[courseName] == CourseStatus.GO) {
                    state.tableActiveCourses.toMutableMap().apply {
                        this[currentTableId] = courseName
                    }
                } else {
                    state.tableActiveCourses
                }
            }

            setState {
                copy(
                    tableCourseStatusQueues = tableCourseQueue,
                    tableAvailableCourses = updatedTableCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                    tableCourseStatuses = updatedTableCourseStatuses
                )
            }
        } else {
            // Handle global courses
            val courseNumber = state.availableCourses.size + 1
            val newCourseId = "Course $courseNumber${courseNumber + 1}"
            val course = state.availableCourses.size

            val courseName =
                when (course) {
                    1 -> {
                        "Starters"
                    }

                    2 -> "Mains"
                    3 -> "Desserts"
                    else -> "Course $courseNumber"
                }

            val sortOrder = when (courseName) {
                "Starters" -> 1
                "Mains" -> 2
                "Desserts" -> 3
                else -> courseNumber + 3 // For courses beyond desserts
            }

            val newCourse = MealCourse(
                name = courseName,
                sortOrder = sortOrder
            )

            var queue = state.getStatusQueue()

            val updatedCourses = state.availableCourses.toMutableList()
            updatedCourses.add(newCourse)

            // Auto-select the newly added course for new items
            val newSelectedCourse = courseName // Use course name for consistency

            // Check if previous course has Preparing status and set new course to Go
            // Also set first course (Course 1) to Go status
            val updatedCourseStatuses = state.courseStatuses.toMutableMap()
            if (courseNumber == 1) {
                // First course should always have Go status
                updatedCourseStatuses[courseName] = CourseStatus.GO
                queue = queue.addToGoQueue(courseId = courseName)
            } else if (state.availableCourses.isNotEmpty()) {
                val previousCourse = state.availableCourses.last().name
                val previousCourseStatus = state.courseStatuses[previousCourse] ?: CourseStatus.GO
                if (previousCourseStatus == CourseStatus.PREPARING) {
                    // Set new course to Go status
                    updatedCourseStatuses[courseName] = CourseStatus.GO
                    queue = queue.addToGoQueue(courseId = courseName)
                } else {
                    // Add new course to go queue regardless of previous course status
                    queue = queue.addToGoQueue(courseId = courseName)
                }
            } else {
                // Add new course to go queue for any new course
                queue = queue.addToGoQueue(courseId = courseName)
            }

            val newCurrentActiveCourse = if (courseNumber == 1) {
                newCourseId
            } else {
                // If previous course is Preparing and new course is Go, make new course active
                val previousCourse =
                    if (state.availableCourses.isNotEmpty()) state.availableCourses.last().name else null
                val previousCourseStatus =
                    if (previousCourse != null) state.courseStatuses[previousCourse]
                        ?: CourseStatus.GO else CourseStatus.GO
                if (previousCourseStatus == CourseStatus.PREPARING && updatedCourseStatuses[courseName] == CourseStatus.GO) {
                    courseName
                } else {
                    state.currentActiveCourse
                }
            }

            setState {
                copy(
                    courseStatusQueue = queue,
                    availableCourses = updatedCourses,
                    selectedCourseForNewItems = newSelectedCourse,
                    currentActiveCourse = newCurrentActiveCourse,
                    courseStatuses = updatedCourseStatuses
                )
            }
        }
    }

    /**
     * Ensure at least one course exists, creating "Course 1" if needed
     */
    private fun ensureAtLeastOneCourse(state: ProductsScreenState): ProductsScreenState {
        return state
    }

    /**
     * Set the selected course for new items (table-specific or global)
     */
    fun setSelectedCourseForNewItems(courseId: String) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific selected course
                val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
                updatedTableSelectedCourses[currentTableId] = courseId
                copy(tableSelectedCourseForNewItems = updatedTableSelectedCourses)
            } else {
                // Update global selected course
                copy(selectedCourseForNewItems = courseId)
            }
        }
    }

    /**
     * Remove a course from available courses
     * @param courseId The ID of the course to remove
     * @return true if course was removed, false if it's the last course
     */
    fun removeCourse(courseId: String, state: ProductsScreenState): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        val currentCourses = if (currentTableId != null) {
            state.tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            state.availableCourses
        }

        val targetOrder = if (currentTableId != null) {
            state.tableOrders[currentTableId]
        } else {
            state.order
        }



        return if (currentCourses.size <= 1) {
            clearCartAndRemoveTable(state = state, currentTableId = currentTableId)
            true
        } else {

            var currentQueue = state.getStatusQueue()
            currentQueue = currentQueue.removeFromGoQueue(courseId = courseId)
            currentQueue = currentQueue.removeFromPreparingQueue(courseId = courseId)
            currentQueue = currentQueue.removeFromCompleteQueue(courseId = courseId)

            val carts = targetOrder?.carts?.toMutableList() ?: mutableListOf()
            carts.removeAll { it.storeItem?.courseId == courseId }

            setState {
                if (currentTableId != null) {
                    // Handle table-specific course removal
                    val updatedCourses = currentCourses.toMutableList()
                    updatedCourses.removeAll { it.name == courseId }

                    // Update table-specific courses
                    val updatedTableCourses = tableAvailableCourses.toMutableMap()
                    updatedTableCourses[currentTableId] = updatedCourses

                    // If the removed course was the selected course for new items, select the first remaining course
                    val updatedTableSelectedCourses =
                        if (tableSelectedCourseForNewItems[currentTableId] == courseId) {
                            tableSelectedCourseForNewItems.toMutableMap().apply {
                                this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                            }
                        } else {
                            tableSelectedCourseForNewItems
                        }

                    // Clean up table-specific course statuses and active courses for the removed course
                    val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
                    val currentTableStatuses =
                        updatedTableCourseStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
                    currentTableStatuses.remove(courseId)
                    updatedTableCourseStatuses[currentTableId] = currentTableStatuses

                    val updatedTableCourseStatusQueues = tableCourseStatusQueues.toMutableMap()
                    updatedTableCourseStatusQueues[currentTableId] = currentQueue

                    val updatedTableActiveCourses =
                        if (tableActiveCourses[currentTableId] == courseId) {
                            tableActiveCourses.toMutableMap().apply {
                                this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                            }
                        } else {
                            tableActiveCourses
                        }

                    val tableOrders = tableOrders.toMutableMap()
                    tableOrders[currentTableId] = targetOrder?.copy(carts = carts) ?: Order()

                    copy(
                        tableAvailableCourses = updatedTableCourses,
                        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                        tableCourseStatuses = updatedTableCourseStatuses,
                        tableActiveCourses = updatedTableActiveCourses,
                        tableOrders = tableOrders,
                        tableCourseStatusQueues = updatedTableCourseStatusQueues
                    )
                } else {
                    // Handle global course removal
                    val updatedCourses = availableCourses.toMutableList()
                    updatedCourses.removeAll { it.name == courseId }

                    // If the removed course was the selected course for new items, select the first remaining course
                    val newSelectedCourse = if (selectedCourseForNewItems == courseId) {
                        updatedCourses.firstOrNull()?.name ?: ""
                    } else {
                        selectedCourseForNewItems
                    }

                    // Clean up course statuses and active courses for the removed course
                    val updatedCourseStatuses = courseStatuses.toMutableMap()
                    updatedCourseStatuses.remove(courseId)

                    val updatedTableCourseStatuses =
                        tableCourseStatuses.mapValues { (_, courseStatuses) ->
                            courseStatuses.toMutableMap().apply { remove(courseId) }
                        }

                    val updatedTableActiveCourses =
                        tableActiveCourses.mapValues { (_, activeCourse) ->
                            if (activeCourse == courseId) {
                                updatedCourses.firstOrNull()?.name ?: ""
                            } else {
                                activeCourse
                            }
                        }

                    val newCurrentActiveCourse = if (currentActiveCourse == courseId) {
                        updatedCourses.firstOrNull()?.name
                    } else {
                        currentActiveCourse
                    }

                    copy(
                        availableCourses = updatedCourses,
                        selectedCourseForNewItems = newSelectedCourse,
                        courseStatuses = updatedCourseStatuses,
                        tableCourseStatuses = updatedTableCourseStatuses,
                        tableActiveCourses = updatedTableActiveCourses,
                        currentActiveCourse = newCurrentActiveCourse,
                        order = targetOrder?.copy(carts = carts) ?: Order(),
                        courseStatusQueue = currentQueue
                    )
                }
            }
            true
        }
    }

    /**
     * Edit an existing course (table-specific or global)
     * @param courseId The ID of the course to edit
     * @param newName The new name for the course
     */
    fun editCourse(courseId: String, newName: String) {
        setState {
            val currentTableId = getCurrentTableId()
            if (currentTableId != null) {
                // Update table-specific course
                val currentTableCourses = tableAvailableCourses[currentTableId] ?: emptyList()
                val updatedCourses = currentTableCourses.map { course ->
                    if (course.name == courseId) {
                        course.copy(name = newName)
                    } else {
                        course
                    }
                }
                val updatedTableCourses = tableAvailableCourses.toMutableMap()
                updatedTableCourses[currentTableId] = updatedCourses
                copy(tableAvailableCourses = updatedTableCourses)
            } else {
                // Update global course
                val updatedCourses = availableCourses.map { course ->
                    if (course.name == courseId) {
                        course.copy(name = newName)
                    } else {
                        course
                    }
                }
                copy(availableCourses = updatedCourses)
            }
        }
    }

    /**
     * Update the status of a specific course
     * @param courseId The ID of the course to update
     * @param status The new status for the course
     */
    fun updateCourseStatus(
        courseId: String,
        status: CourseStatus,
        state: ProductsScreenState
    ): ProductsScreenState {
//        val currentTableId = state.getCurrentTableId()
//        val updatedState = if (currentTableId != null) {
//            // Update table-specific course status
//            val updatedTableStatuses = state.tableCourseStatuses.toMutableMap()
//            val currentTableStatuses =
//                updatedTableStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
//            currentTableStatuses[courseId] = status
//            updatedTableStatuses[currentTableId] = currentTableStatuses
//            setState {
//                copy(tableCourseStatuses = updatedTableStatuses)
//            }
//            state.copy(
//                tableCourseStatuses = updatedTableStatuses
//            )
//        } else {
//            // Update global course status (for walk-in customers)
//            val updatedStatuses = state.courseStatuses.toMutableMap()
//            updatedStatuses[courseId] = status
//            setState {
//                copy(courseStatuses = updatedStatuses)
//            }
//            state.copy(
//                courseStatuses = updatedStatuses
//            )
//        }
//
//        // Apply course logic: When Course 1 is Preparing, next course should be Go
//        val newState = applyCoursePreparingLogic(updatedState, courseId, status, currentTableId)
//        setState {
//            copy(
//                tableCourseStatuses = newState.tableCourseStatuses,
//                tableActiveCourses = newState.tableActiveCourses,
//                courseStatuses = newState.courseStatuses,
//                currentActiveCourse = newState.currentActiveCourse
//            )
//        }
        return state
    }

    /**
     * Apply course logic using queue system: When a course moves to Preparing, next course should be Go
     */
    private fun applyCoursePreparingLogic(
        state: ProductsScreenState,
        courseId: String,
        status: CourseStatus,
        currentTableId: Int?
    ): ProductsScreenState {
        // Get available courses for current context
        val availableCourses = if (currentTableId != null) {
            state.tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            state.availableCourses
        }

        if (availableCourses.isEmpty()) return state

        var queue = state.getStatusQueue()

        // If we're updating a course to Preparing status, use queue system
        if (status == CourseStatus.PREPARING) {
            // Move course from GO queue to PREPARING queue
//            moveCourseFromGoToPreparing(courseId, currentTableId, state)
            queue = queue.moveFromGoToPreparing(courseId = courseId)

            // Add next course to GO queue
//            addNextCourseToGoQueue(availableCourses, courseId, currentTableId, state)

            // Update the next course status to GO and make it active
            val currentIndex = availableCourses.indexOfFirst { it.name == courseId }
            if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
                val nextCourse = availableCourses[currentIndex + 1].name

                queue = queue.addToGoQueue(courseId = nextCourse)

                return if (currentTableId != null) {

                    val updateStatusQueues = state.tableCourseStatusQueues.toMutableMap()
                    updateStatusQueues[currentTableId] = queue

                    // Update table-specific course status
                    val updatedTableStatuses = state.tableCourseStatuses.toMutableMap()
                    val currentTableStatuses =
                        updatedTableStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
                    currentTableStatuses[nextCourse] = CourseStatus.GO
                    updatedTableStatuses[currentTableId] = currentTableStatuses

                    // Also update active course to the next course
                    val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
                    updatedTableActiveCourses[currentTableId] = nextCourse

                    setState {
                        copy(
                            tableCourseStatuses = updatedTableStatuses,
                            tableActiveCourses = updatedTableActiveCourses,
                            tableCourseStatusQueues = updateStatusQueues
                        )
                    }

                    state.copy(
                        tableCourseStatuses = updatedTableStatuses,
                        tableActiveCourses = updatedTableActiveCourses,
                        tableCourseStatusQueues = updateStatusQueues
                    )
                } else {
                    // Update global course status
                    val updatedStatuses = state.courseStatuses.toMutableMap()
                    updatedStatuses[nextCourse] = CourseStatus.GO
                    setState {
                        copy(
                            courseStatusQueue = queue,
                            courseStatuses = updatedStatuses,
                            currentActiveCourse = nextCourse
                        )
                    }
                    state.copy(
                        courseStatusQueue = queue,
                        courseStatuses = updatedStatuses,
                        currentActiveCourse = nextCourse
                    )
                }
            }
        }

        return state
    }

//    /**
//     * Apply Go button click logic for different courses (supports both global and table-based courses)
//     */
//    private fun applyGoClickLogic(
//        courseId: String,
//        currentTableId: Int?,
//        state: ProductsScreenState
//    ) {
//        val availableCourses = if (currentTableId != null) {
//            state.tableAvailableCourses[currentTableId]?.toMutableList() ?: mutableListOf()
//        } else state.availableCourses.toMutableList()
//        if (availableCourses.isEmpty()) return
//
//        // Find Course 1 and Course 2
//        val course1 = availableCourses.firstOrNull()?.name
//        val course2 = if (availableCourses.size > 1) availableCourses[1].name else null
//
//        when (courseId) {
//            course1 -> {
//                // Course 1 Go click logic: next course should be Go
//                if (course2 != null) {
//                    val course1Status = getCourseStatus(courseId, state)
//                    if (course1Status == CourseStatus.GO) {
//                        // Set Course 2 to Go status (works for both table-based and global)
//                        updateCourseStatus(course2, CourseStatus.GO, state = state)
//
//                        // Update active course to Course 2
//                        if (currentTableId != null) {
//                            setState {
//                                val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
//                                updatedTableActiveCourses[currentTableId] = course2
//                                copy(tableActiveCourses = updatedTableActiveCourses)
//                            }
//                        } else {
//                            // Update global active course
//                            setState {
//                                copy(currentActiveCourse = course2)
//                            }
//                        }
//                    }
//                }
//            }
//
//            course2 -> {
//                // Course 2 Go click logic: if Course 2 is clicked in Go status, mark Course 1 as Complete
//                if (course1 != null) {
//                    val course2Status = getCourseStatus(courseId, state)
//                    if (course2Status == CourseStatus.GO) {
//                        // Mark Course 1 as Complete (works for both table-based and global)
//                        updateCourseStatus(course1, CourseStatus.COMPLETE, state = state)
//                    }
//                }
//            }
//
//            else -> {
//                // For other courses, find the previous course and mark it complete if this course is Go
//                val currentCourseIndex = availableCourses.indexOfFirst { it.name == courseId }
//                if (currentCourseIndex > 0) {
//                    val previousCourse = availableCourses[currentCourseIndex - 1].name
//                    val currentCourseStatus = getCourseStatus(courseId, state)
//                    if (currentCourseStatus == CourseStatus.GO) {
//                        // Mark previous course as Complete (works for both table-based and global)
//                        updateCourseStatus(previousCourse, CourseStatus.COMPLETE, state = state)
//                    }
//                }
//            }
//        }
//    }

    /**
     * Get the status of a specific course
     * @param courseId The ID of the course
     * @return The current status of the course, defaults to GO if not set
     */
    fun getCourseStatus(courseId: String, state: ProductsScreenState): CourseStatus {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId != null) {
            // Get table-specific course status
            state.tableCourseStatuses[currentTableId]?.get(courseId) ?: CourseStatus.GO
        } else {
            // Get global course status (for walk-in customers)
            state.courseStatuses[courseId] ?: CourseStatus.GO
        }
    }

    /**
     * Remove course statuses for a specific table
     * @param tableId The ID of the table to remove course statuses for
     */
    fun removeTableCourseStatuses(tableId: Int) {
        setState {
            val updatedTableStatuses = tableCourseStatuses.toMutableMap()
            updatedTableStatuses.remove(tableId)

            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            updatedTableActiveCourses.remove(tableId)

            copy(
                tableCourseStatuses = updatedTableStatuses,
                tableActiveCourses = updatedTableActiveCourses
            )
        }
    }

    /**
     * Clear all course statuses for all tables
     */
    fun clearAllTableCourseStatuses() {
        setState {
            copy(
                tableCourseStatuses = emptyMap(),
                tableActiveCourses = emptyMap()
            )
        }
    }

    /**
     * Get course statuses for the current table or global statuses
     * @return Map of course statuses for the current context
     */
    fun getCurrentCourseStatuses(state: ProductsScreenState): Map<String, CourseStatus> {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId != null) {
            state.tableCourseStatuses[currentTableId] ?: emptyMap()
        } else {
            state.courseStatuses
        }
    }

    /**
     * Handle table selection change - initialize course statuses for new table if needed
     * @param tableId The ID of the newly selected table
     */
    fun onTableSelected(tableId: Int) {
        setState {
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            // Always set first course as active for new table
            updatedTableActiveCourses[tableId] = availableCourses.firstOrNull()?.name ?: ""
            copy(tableActiveCourses = updatedTableActiveCourses)
        }
    }

    /**
     * Handle table removal - clean up course statuses for the removed table
     * @param tableId The ID of the table being removed
     */
    fun onTableRemoved(tableId: Int) {
        removeTableCourseStatuses(tableId)
    }

    /**
     * Reset course statuses for the current table
     */
    fun resetCurrentTableCourseStatuses(state: ProductsScreenState) {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        if (currentTableId != null) {
            removeTableCourseStatuses(currentTableId)
            // Re-initialize the table
            onTableSelected(currentTableId)
        } else {
            // Reset global statuses
            setState {
                copy(
                    courseStatuses = emptyMap(),
                    currentActiveCourse = availableCourses.firstOrNull()?.name
                )
            }
        }
    }

    /**
     * Check if there are items in the global cart (when no table is selected)
     * @return true if there are items in the global cart
     */
    fun hasGlobalCartItems(state: ProductsScreenState): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        return if (currentTableId == null) {
            // No table selected - check global cart
            val globalCartItems = state.order.carts ?: emptyList()
            val globalCartItemsWithCourses = state.globalCartItemsWithCourses
            globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty()
        } else {
            // Table is selected - no global cart items concern
            false
        }
    }

    /**
     * Clear global cart and course assignments (used when switching from walk-in to table mode)
     */
    fun clearGlobalCart() {
        setState {
            copy(
                order = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                ),
                globalCartItemsWithCourses = emptyList(),
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.name
            )
        }
    }

    /**
     * Assign all global cart items to a selected table
     * This transfers items from global cart to table-specific cart
     */
    fun assignGlobalCartToTable(
        selection: AreaTableSelectionHelper.AreaTableSelection,
        state: ProductsScreenState
    ): ProductsScreenState {

        val globalOrder = state.order
        val globalCartItems = state.globalCartItemsWithCourses

        // Add the table if it doesn't exist
        val updatedTables = state.selectedTables.toMutableList()
        if (!updatedTables.any { it.tableId == selection.tableId }) {
            updatedTables.add(selection)
        }

        // Initialize or update table order with global cart items
        val updatedTableOrders = state.tableOrders.toMutableMap()
        updatedTableOrders[selection.tableId] = globalOrder

        // Initialize table course assignments with global cart items
        val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
        updatedCartItemsWithCourses[selection.tableId] = globalCartItems

        // Initialize other table-specific data
        val updatedFilters = state.selectedCourseFilter.toMutableMap()
        val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
        val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

        if (!updatedFilters.containsKey(selection.tableId)) {
            updatedFilters[selection.tableId] = CourseFilter.ALL
        }
        if (!updatedTableCourses.containsKey(selection.tableId)) {
            updatedTableCourses[selection.tableId] = state.availableCourses
        }
        if (!updatedTableSelectedCourses.containsKey(selection.tableId)) {
            updatedTableSelectedCourses[selection.tableId] =
                state.availableCourses.firstOrNull()?.name ?: ""
        }
        if (!updatedTableActiveCourses.containsKey(selection.tableId)) {
            updatedTableActiveCourses[selection.tableId] =
                state.availableCourses.firstOrNull()?.name ?: ""
        }

        setState {
            // Clear global cart and assign to table
            copy(
                selectedTables = updatedTables,
                selectedTableIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId },
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters,
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses,
                // Clear global cart
                order = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                ),
                globalCartItemsWithCourses = emptyList(),
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.name,
                showCart = true // Show cart after assignment
            )
        }

        return state.copy(
            selectedTables = updatedTables,
            selectedTableIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId },
            tableOrders = updatedTableOrders,
            cartItemsWithCourses = updatedCartItemsWithCourses,
            selectedCourseFilter = updatedFilters,
            tableAvailableCourses = updatedTableCourses,
            tableSelectedCourseForNewItems = updatedTableSelectedCourses,
            tableActiveCourses = updatedTableActiveCourses,
            // Clear global cart
            order = Order(
                carts = emptyList(),
                netPayable = 0.0,
                tax = 0.0,
                totalPrice = 0.0
            ),
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = state.availableCourses.firstOrNull()?.name,
            showCart = true // Show cart after assignment
        )
    }

    /**
     * Initialize the first course as active (with Go button)
     */
    fun initializeActiveCourse() {
        setState {
            val firstCourse = availableCourses.firstOrNull()?.name
            val currentTableId = getCurrentTableId()

            if (currentTableId != null) {
                // Initialize table-specific active course to first course
                val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                updatedTableActiveCourses[currentTableId] = firstCourse ?: ""
                copy(tableActiveCourses = updatedTableActiveCourses)
            } else {
                // Initialize global active course to first course (for walk-in customers)
                copy(currentActiveCourse = firstCourse)
            }
        }
    }

    /**
     * Implement Send to Kitchen course logic using preparing queue:
     * - Course 1 -> Preparing Queue, Course 2 -> Go, Course 3 -> Waiting
     * - Multiple courses can be in preparing queue
     */
    private suspend fun implementSendToKitchenCourseLogic(
        availableCourses: List<MealCourse>?,
        currentTableId: Int?,
        state: ProductsScreenState
    ) : ProductsScreenState {
        var updatedState = state
        // Remove empty course from available courses
        val filteredCourses = availableCourses?.filter { it.name.isNotEmpty() }

        // Find Course 1 (first course)
        val course1 = "Starters"
        val course2 = if ((filteredCourses?.size ?: 0) > 1) filteredCourses?.get(1)?.name else null

        if (course1 != null) {

            // Initialize queue if not already done
            var currentQueue = state.getStatusQueue()

            // If Course 1 is Go, move it to Preparing queue and set Course 2 to Go
            if (shouldShowGoButton(courseId = course1, state = state)
                && !shouldShowCompleteButton(courseId = course1, state = state)
                && !shouldShowPreparing(courseId = course1, state = state)
                && !shouldShowCompleteStatus(courseId = course1, state = state)
            ) {
                // Set Course 1 to Preparing
                updateCourseStatus(course1, CourseStatus.PREPARING, state = state)
                currentQueue = currentQueue.moveFromGoToPreparing(courseId = course1)

                // Move Course 1 from GO queue to PREPARING queue
//                moveCourseFromGoToPreparing(course1, currentTableId, state)

                // Set Course 2 to Go (if it exists) and add to GO queue
                if (course2 != null) {
                    updateCourseStatus(course2, CourseStatus.GO, state = state)
                    // Add Course 2 to GO queue
                    val updatedQueue = currentQueue.addToGoQueue(course2)
                    updateCourseStatusQueue(updatedQueue, currentTableId)

                    // Update active course to Course 2
                    setState {
                        if (currentTableId != null) {
                            val updatedTableQueue = tableCourseStatusQueues.toMutableMap()
                            updatedTableQueue[currentTableId] = updatedQueue
                            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
                            updatedTableActiveCourses[currentTableId] = course2
                            copy(
                                tableActiveCourses = updatedTableActiveCourses,
                                tableCourseStatusQueues = updatedTableQueue
                            )
                        } else {
                            copy(
                                currentActiveCourse = course2,
                                courseStatusQueue = updatedQueue
                            )
                        }
                    }

                    if (currentTableId != null) {
                        val updatedTableQueue = state.tableCourseStatusQueues.toMutableMap()
                        updatedTableQueue[currentTableId] = updatedQueue
                        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
                        updatedTableActiveCourses[currentTableId] = course2
                        updatedState = state.copy(
                            tableActiveCourses = updatedTableActiveCourses,
                            tableCourseStatusQueues = updatedTableQueue
                        )
                    } else {
                        updatedState = state.copy(
                            currentActiveCourse = course2,
                            courseStatusQueue = updatedQueue
                        )
                    }

                } else {
                    val updatedQueue = currentQueue
                    updateCourseStatusQueue(updatedQueue, currentTableId)

                    // Update active course to Course 2
                    setState {
                        if (currentTableId != null) {
                            val updatedTableQueue = tableCourseStatusQueues.toMutableMap()
                            updatedTableQueue[currentTableId] = updatedQueue
                            copy(
                                tableCourseStatusQueues = updatedTableQueue
                            )
                        } else {
                            copy(
                                courseStatusQueue = updatedQueue
                            )
                        }
                    }

                    if (currentTableId != null) {
                        val updatedTableQueue = state.tableCourseStatusQueues.toMutableMap()
                        updatedTableQueue[currentTableId] = updatedQueue
                        updatedState = state.copy(
                            tableCourseStatusQueues = updatedTableQueue
                        )
                    } else {
                        updatedState = state.copy(
                            courseStatusQueue = updatedQueue
                        )
                    }

                }

                println("Send to Kitchen: Course 1 -> Preparing Queue, Course 2 -> Go")
            }
        }
        return updatedState
    }

    /**
     * Move the Go button to the next course in sequence
     * @param currentCourseId The course that was just processed
     */
    fun moveToNextCourse(currentCourseId: String, state: ProductsScreenState) {
        val availableCourses = if (state.getCurrentTableId() != null) {
            state.tableAvailableCourses[state.getCurrentTableId()] ?: mutableListOf()
        } else state.availableCourses
        val currentTableId = state.getCurrentTableId()
        val currentIndex = availableCourses.indexOfFirst { it.name == currentCourseId }
        val nextCourse = if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
            availableCourses[currentIndex + 1].name
        } else {
            // If we're at the last course, cycle back to the first course
            availableCourses.firstOrNull()?.name
        }

        if (currentTableId != null) {
            // Update table-specific active course
            val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
            updatedTableActiveCourses[currentTableId] = nextCourse ?: ""
            setState {
                copy(tableActiveCourses = updatedTableActiveCourses)
            }
        } else {
            setState {
                // Update global active course (for walk-in customers)
                copy(currentActiveCourse = nextCourse)
            }
        }

    }

    // ==================== QUEUE MANAGEMENT FUNCTIONS ====================

    /**
     * Initialize course status queues based on available courses
     * Sets up the initial queue state with Course 1 in GO queue, others in WAITING
     */
    fun initializeCourseStatusQueues(
        availableCourses: List<MealCourse>,
        currentTableId: Int?
    ) {
        if (availableCourses.isEmpty()) return

        // Create initial queue with first course in GO queue
        val initialQueue = CourseStatusQueue(
            goQueue = listOf(availableCourses.first().name),
            preparingQueue = mutableListOf()
        )

        if (currentTableId != null) {
            // Update table-specific queue
            setState {
                val updatedQueues = tableCourseStatusQueues.toMutableMap()
                updatedQueues[currentTableId] = initialQueue
                copy(tableCourseStatusQueues = updatedQueues)
            }
        } else {
            // Update global queue
            setState {
                copy(courseStatusQueue = initialQueue)
            }
        }
    }

    /**
     * Update the course status queue for the given context
     */
    fun updateCourseStatusQueue(
        newQueue: CourseStatusQueue,
        currentTableId: Int?
    ) {
        if (currentTableId != null) {
            setState {
                val updatedQueues = tableCourseStatusQueues.toMutableMap()
                updatedQueues[currentTableId] = newQueue
                copy(tableCourseStatusQueues = updatedQueues)
            }
        } else {
            setState {
                copy(courseStatusQueue = newQueue)
            }
        }
    }

    /**
     * Move course from GO queue to PREPARING queue
     * Marks ALL previous courses as complete
     */
    fun moveCourseFromGoToPreparing(
        courseId: String,
        currentTableId: Int?,
        state: ProductsScreenState
    ) {
        val currentQueue = state.getStatusQueue()

        // Get available courses for current context
        val availableCourses = if (currentTableId != null) {
            state.tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            state.availableCourses
        }

        val allCourseNames = availableCourses.map { it.name }
        val updatedQueue =
            currentQueue.moveFromGoToPreparingWithAllPreviousComplete(courseId, allCourseNames)

        // Mark ALL previous courses as complete in the status map
        val currentIndex = allCourseNames.indexOf(courseId)
        if (currentIndex > 0) {
            val previousCourses = allCourseNames.subList(0, currentIndex)
            previousCourses.forEach { previousCourse ->
                updateCourseStatus(previousCourse, CourseStatus.COMPLETE, state = state)
            }
        }

        updateCourseStatusQueue(updatedQueue, currentTableId)
    }

    /**
     * Complete all courses in the preparing queue
     */
    fun completeAllPreparingCourses(currentTableId: Int?, state: ProductsScreenState) {
        val currentQueue = state.getStatusQueue()
        if (currentQueue.preparingQueue.isNotEmpty()) {
            val updatedQueue = currentQueue.completeAllPreparingCourses()
            // Mark all preparing courses as complete in the status map
            currentQueue.preparingQueue.forEach { courseId ->
                updateCourseStatus(courseId, CourseStatus.COMPLETE, state = state)
            }
            updateCourseStatusQueue(updatedQueue, currentTableId)
        }
    }

    /**
     * Complete a specific course (if it's in the preparing queue)
     */
    fun completeCourse(courseId: String, currentTableId: Int?, state: ProductsScreenState) {
        val currentQueue = state.getStatusQueue()
        if (currentQueue.isInPreparingQueue(courseId)) {
            val updatedQueue = currentQueue.completeSpecificCourse(courseId)
            updateCourseStatus(courseId, CourseStatus.COMPLETE, state = state)
            updateCourseStatusQueue(updatedQueue, currentTableId)
        }
    }

    /**
     * Add next course to GO queue when current course moves to PREPARING
     */
    fun addNextCourseToGoQueue(
        availableCourses: List<MealCourse>,
        currentCourseId: String,
        currentTableId: Int?,
        state: ProductsScreenState
    ) {
        val currentIndex = availableCourses.indexOfFirst { it.name == currentCourseId }
        if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
            val nextCourse = availableCourses[currentIndex + 1].name
            val currentQueue = state.getStatusQueue()
            val updatedQueue = currentQueue.addToGoQueue(nextCourse)
            updateCourseStatusQueue(updatedQueue, currentTableId)
        }
    }

    /**
     * Apply Go button click logic with preparing queue
     * When a course Go button is clicked:
     * 1. ALL previous courses are marked as Complete
     * 2. Current course moves to Preparing queue
     * 3. Next course moves to Go (if exists)
     */
    private fun applyGoClickQueueLogic(
        courseId: String,
        availableCourses: List<MealCourse>,
        currentTableId: Int?,
        state: ProductsScreenState
    ): ProductsScreenState {
        var updateState = state
        val currentIndex = availableCourses.indexOfFirst { it.name == courseId }

        var queue = state.getStatusQueue()

        // Step 1: Mark ALL previous courses as Complete
        if (currentIndex > 0) {
            val previousCourses = availableCourses.subList(0, currentIndex)
            queue = queue.completeAllPreparingCourses()
//            previousCourses.forEach { previousCourse ->
//                updateCourseStatus(previousCourse.name, CourseStatus.COMPLETE, state = state)
//                println("Auto-completing previous course: ${previousCourse.name}")
//            }
        }

        // Step 2: Move current course from GO to PREPARING queue
        queue = queue.moveFromGoToPreparing(courseId = courseId)
//        moveCourseFromGoToPreparing(courseId, currentTableId, state)

        // Step 3: Find next course and set up if exists
        if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
            // Not the last course - set next course to GO
            val nextCourse = availableCourses[currentIndex + 1].name

            queue = queue.addToGoQueue(courseId = nextCourse)

            // Update next course status to GO
//            updateCourseStatus(nextCourse, CourseStatus.GO, state = state)

            // Add next course to GO queue
//            addNextCourseToGoQueue(availableCourses, courseId, currentTableId, state)

            // Update active course to next course
            if (currentTableId != null) {
                val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
                updatedTableActiveCourses[currentTableId] = nextCourse
                val tableStatusQueue = state.tableCourseStatusQueues.toMutableMap()
                tableStatusQueue[currentTableId] = queue

                updateState = state.copy(
                    tableActiveCourses = updatedTableActiveCourses,
                    tableCourseStatusQueues = tableStatusQueue
                )
                setState {
                    copy(
                        tableActiveCourses = updatedTableActiveCourses,
                        tableCourseStatusQueues = tableStatusQueue
                    )
                }
            } else {
                updateState = state.copy(
                    currentActiveCourse = nextCourse,
                    courseStatusQueue = queue
                )
                setState {
                    copy(
                        currentActiveCourse = nextCourse,
                        courseStatusQueue = queue
                    )
                }
            }

            println("Course progression: All previous -> Complete, $courseId -> Preparing Queue, $nextCourse -> Go")
        } else if (currentIndex == availableCourses.size - 1) {
            // This is the last course - it goes to preparing queue
            println("Last course ($courseId) is now in preparing queue")
            println("All previous courses have been marked as Complete")
            queue = queue.moveFromGoToPreparing(availableCourses[currentIndex].name)
            if (currentTableId != null) {
                val tableStatusQueue = state.tableCourseStatusQueues.toMutableMap()
                tableStatusQueue[currentTableId] = queue

                updateState = state.copy(
                    tableCourseStatusQueues = tableStatusQueue
                )

                setState {
                    copy(
                        tableCourseStatusQueues = tableStatusQueue
                    )
                }
            } else {
                updateState = state.copy(
                    courseStatusQueue = queue
                )
                setState {
                    copy(courseStatusQueue = queue)
                }
            }
        }
        return updateState
    }

    /**
     * Check if a course should show the Complete status
     * @param courseId The course to check
     * @return true if this course should show Complete status (not in GO queue and not in preparing queue)
     */
    fun shouldShowCompleteStatus(
        courseId: String,
        state: ProductsScreenState
    ): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )

        // Get current queue
        val currentQueue = state.getStatusQueue()

        // Show Complete status if course is not in GO queue and not in preparing queue
        return currentQueue.shouldShowAsComplete(courseId)
    }

    /**
     * Check if a course should show the Complete button (for the last course when preparing)
     * @param courseId The course to check
     */
    fun shouldShowCompleteButton(
        courseId: String,
        state: ProductsScreenState
    ): Boolean {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        // Get available courses for current context
        val availableCourses = if (currentTableId != null) {
            state.tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            state.availableCourses
        }
        if (availableCourses.isEmpty()) return false
        val isLastCourse = availableCourses.last().name == courseId
        // Get current queue
        val currentQueue = state.getStatusQueue()
        val isPreparing = currentQueue.isInPreparingQueue(courseId)
        val courseStatus = getCourseStatus(courseId, state)
        return isLastCourse && isPreparing && courseStatus == CourseStatus.GO
    }

    /**
     * Handle Complete button click - mark course as complete
     */
    fun markCourseAsComplete(courseId: String, state: ProductsScreenState) {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        // Update course status to COMPLETE
//        updateCourseStatus(courseId, CourseStatus.COMPLETE, state = state)
        // Remove course from PREPARING queue and add to completed
        val updatedQueue =
            state.getStatusQueue().completeSpecificCourse(courseId)
        updateCourseStatusQueue(updatedQueue, currentTableId)
    }


//    fun shouldShowComplete(
//        courseId: String,
//        state: ProductsScreenState
//    ): Boolean {
//        val currentTableId = getCurrentTableId(
//            selectedTables = state.selectedTables,
//            selectedTableIndex = state.selectedTableIndex
//        )
//
//        // Get available courses for current context
//        val availableCourses = if (currentTableId != null) {
//            state.tableAvailableCourses[currentTableId] ?: emptyList()
//        } else {
//            state.availableCourses
//        }
//
//        // Get current queue
//        val currentQueue = getCourseStatusQueue(currentTableId, state)
//        val isCurrentlyPreparing =
//            currentQueue.preparingQueue.firstOrNull { it == courseId } != null
//
//        return !isCurrentlyPreparing
//    }


    /**
     * Send courses notification to printer for a specific course
     * @param courseId The ID of the course to send notification for
     */
    suspend fun sendCoursesNotificationForCourse(
        courseId: String, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        availableCourses: List<MealCourse>,
        state: ProductsScreenState
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())

        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )
            val tableName = if (currentTableId != null) {
                if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                    selectedTables[selectedTableIndex].tableName
                } else {
                    "Table-$currentTableId"
                }
            } else {
                "Walk-in"
            }

            // Get cart items based on whether a table is selected or not
            val filteredCourseCartItems = if (currentTableId != null) {
                val items = cartItemsWithCourses[currentTableId] ?: mutableListOf()
                // Table is selected - use table-specific cart items
                items.filter {
                    it.courseId == courseId
                }
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses.filter { it.courseId == courseId }
            }

            val courseCartItems = filteredCourseCartItems.map {
                // remove voided from notes
                it.copy(cart = it.cart.copy(notes = it.cart.notes?.replace("VOIDED", "")))
            }

            val course = availableCourses.find { it.name == courseId }
            val courseName = course?.name ?: ""

            // Extract just the Cart objects from CartItemWithCourse
            val carts = courseCartItems.map { it.cart }

            if (carts.isNotEmpty()) {

                sendCoursesNotificationUseCase(
                    storeId = storeId,
                    courseName = courseName,
                    tableName = tableName ?: "",
                    carts = carts
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            if (result()()?.success == true) {

                                // Update status to PREPARING when Go button is clicked
                                var updatedState = updateCourseStatus(
                                    courseId,
                                    CourseStatus.PREPARING,
                                    state = state
                                )

                                // Apply queue-based Go click logic
                                updatedState = applyGoClickQueueLogic(
                                    courseId,
                                    availableCourses,
                                    currentTableId,
                                    state = updatedState
                                )

                                // Save/update table order if needed
                                if (currentTableId != null) {
                                    val targetOrder = tableOrders[currentTableId]
                                    val updatedOrder = targetOrder?.copy(
                                        carts = carts,
                                        netPayable = targetOrder.netPayable,
                                        tax = targetOrder.tax,
                                        totalPrice = targetOrder.totalPrice
                                    )
                                    viewModelScope.launch {
                                        updatedOrder?.let {
                                            saveTableOrderIfNeeded(
                                                currentTableId,
                                                updatedOrder,
                                                state = updatedState
                                            )
                                        }
                                    }
                                }

                                // Handle success
                                println("Course notification sent successfully for $courseName")
                                flow.value = result()
                                // You can add logic here to automatically change to COMPLETE after some time
                                // or wait for kitchen confirmation
                            } else {
                                updateCourseStatus(courseId, CourseStatus.GO, state = state)
                                flow.value = Fail(Throwable("Unable to Print Course"))
                            }
                            copy()
                        }

                        is Fail -> {
                            // Handle error - revert status back to GO on failure
                            updateCourseStatus(courseId, CourseStatus.GO, state = state)
                            flow.value = Fail(result.error)
                            println("Failed to send course notification for $courseName: ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            // Loading state
                            println("Sending course notification for $courseName...")
                            copy()
                        }
                    }
                }
            } else {
                println("No items found for course: $courseName")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }

    suspend fun printBill(
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        availableCourses: List<MealCourse>,
        state: ProductsScreenState,
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )

            // Get cart items based on whether a table is selected or not
            val courseCartItems = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                cartItemsWithCourses[currentTableId] ?: mutableListOf()
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses
            }

            // Extract just the Cart objects from CartItemWithCourse
            val carts = courseCartItems.map { it.cart }

            if (carts.isNotEmpty()) {
                printRepository.printBill(
                    request = PrintBillRequest(
                        storeId = storeId,
                        cartJson = printRepository.convertCartsToJson(
                            carts = carts
                        ),
                        tableName = if (currentTableId != null) selectedTables[state.selectedTableIndex].tableName else "",
                        serviceCharge = if (currentTableId != null) state.tableServiceChargeApplied[currentTableId]
                            ?: false else state.serviceChargeApplied
                    ),
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            if (result()()?.success == true) {
                                // Handle success - move Go button to next course
                                println("Print Bill done successfully")
                                flow.value = result()
                                // You can add logic here to automatically change to COMPLETE after some time
                                // or wait for kitchen confirmation
                            } else {
                                flow.value = Fail(Throwable("Unable to Print Bill"))
                            }
                            copy()
                        }

                        is Fail -> {
                            flow.value = Fail(result.error)
                            println("Unable to Print Bill ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            copy()
                        }
                    }
                }
            } else {
                println("No items found")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }

    /**
     * Mark all current cart items as sent to kitchen
     * @param currentTableId The table ID (null for global)
     * @param state Current state
     */
    private fun markAllItemsAsSentToKitchen(currentTableId: Int?, state: ProductsScreenState) {
        setState {
            if (currentTableId != null) {
                // Handle table-specific cart items
                val currentCartItems = cartItemsWithCourses[currentTableId] ?: emptyList()
                val updatedCartItems = currentCartItems.map { cartItemWithCourse ->
                    val updatedCart = cartItemWithCourse.cart.copy(sentToKitchen = true)
                    cartItemWithCourse.copy(cart = updatedCart)
                }

                val updatedTableCartItems = cartItemsWithCourses.toMutableMap()
                updatedTableCartItems[currentTableId] = updatedCartItems

                // Also update the table order
                val currentOrder = tableOrders[currentTableId] ?: Order()
                val updatedCarts = currentOrder.carts?.map { cart ->
                    cart.copy(sentToKitchen = true)
                }
                val updatedOrder = currentOrder.copy(carts = updatedCarts)
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = updatedOrder

                copy(
                    cartItemsWithCourses = updatedTableCartItems,
                    tableOrders = updatedTableOrders
                )
            } else {
                // Handle global cart items
                val updatedGlobalCartItems = globalCartItemsWithCourses.map { cartItemWithCourse ->
                    val updatedCart = cartItemWithCourse.cart.copy(sentToKitchen = true)
                    cartItemWithCourse.copy(cart = updatedCart)
                }

                // Also update the global order
                val updatedCarts = order.carts?.map { cart ->
                    cart.copy(sentToKitchen = true)
                }
                val updatedOrder = order.copy(carts = updatedCarts)

                copy(
                    globalCartItemsWithCourses = updatedGlobalCartItems,
                    order = updatedOrder
                )
            }
        }
    }


    suspend fun sendToKitchen(
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        globalCartItemsWithCourses: List<CartItemWithCourse>,
        state: ProductsScreenState
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex
            )

            // Get cart items based on whether a table is selected or not
            val filteredCourseCartItems = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                val items = cartItemsWithCourses[currentTableId] ?: mutableListOf()
                items.filter { !it.cart.sentToKitchen }
            } else {
                // No table selected - use global cart items
                globalCartItemsWithCourses.filter { !it.cart.sentToKitchen }
            }

            val courseCartItems = filteredCourseCartItems.map {
                // remove voided from notes
                it.copy(cart = it.cart.copy(notes = it.cart.notes?.replace("VOIDED", "")))
            }

            val availableCourses = if (currentTableId != null) {
                // Table is selected - use table-specific cart items
                state.tableAvailableCourses[currentTableId]
            } else {
                // No table selected - use global cart items
                state.availableCourses
            }

            // Extract just the Cart objects from CartItemWithCourse
            // Group cart items by course
            val courseGroups = courseCartItems.groupBy { it.courseId }
            val list = mutableListOf<SendToKitchenItem>()

            courseGroups.forEach { (courseId, items) ->
                val updatedItems = mutableListOf<Cart>()
                items.forEach {
                    updatedItems.add(it.cart)
                }

                val item = SendToKitchenItem(
                    storeId = storeId,
                    courseName = courseId,
                    cartJson = printRepository.convertCartsToJson(carts = updatedItems),
                    tableName = if (currentTableId != null) {
                        if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                            selectedTables[selectedTableIndex].tableName
                        } else {
                            "Table-$currentTableId"
                        }
                    } else {
                        "Walk-in"
                    }
                )
                list.add(item)
            }

            if (list.isNotEmpty()) {

                printRepository.sendToKitchen(
                    request = SendToKitchenRequest(
                        listItems = list
                    ),
                ).execute { result ->
                    when (result) {
                        is Success -> {
                            // Handle success
                            if (result()()?.success == true) {

                                viewModelScope.launch {
                                    // Implement course status logic before sending to kitchen
                                    val updatedState = implementSendToKitchenCourseLogic(
                                        availableCourses,
                                        currentTableId,
                                        state = state
                                    )

                                    // Mark all current cart items as sent to kitchen before sending
                                    markAllItemsAsSentToKitchen(currentTableId, updatedState)

                                    // Save/update table order if needed
                                    if (currentTableId != null) {
                                        val targetOrder = tableOrders[currentTableId]
                                        val updatedOrder = targetOrder?.copy(
                                            carts = targetOrder.carts,
                                            netPayable = targetOrder.netPayable,
                                            tax = targetOrder.tax,
                                            totalPrice = targetOrder.totalPrice
                                        )
                                        viewModelScope.launch {
                                            updatedOrder?.let {
                                                saveTableOrderIfNeeded(
                                                    currentTableId,
                                                    updatedOrder,
                                                    state = updatedState
                                                )
                                            }
                                        }
                                    }
                                }
                                println("Send to Kitchen done successfully")
                                flow.value = result()
                            } else {
                                flow.value = Fail(Throwable("Unable to Send to Kitchen"))
                            }
                            copy()
                        }

                        is Fail -> {
                            println("Send to Kitchen failed: ${result.error.message}")
                            copy()
                        }

                        else -> {
                            flow.value = Uninitialized
                            copy()
                        }
                    }
                }
            } else {
                println("No items found")
            }
        } catch (e: Exception) {
            println("Error sending course notification: ${e.message}")
        }
        return flow
    }

    /**
     * Send courses notification to printer
     * This function will send notification for each course that has items in the current order
     */
    fun sendCoursesNotification(
        selectedTableIndex: Int, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
        availableCourses: List<MealCourse>
    ) {
        viewModelScope.launch {
            try {
                val storeId = prefs.store?.id ?: -1
                val currentTableId = getCurrentTableId(
                    selectedTableIndex = selectedTableIndex,
                    selectedTables = selectedTables
                )
                val tableName = if (currentTableId != null) {
                    if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                        selectedTables[selectedTableIndex].tableName
                    } else {
                        "Table-$currentTableId"
                    }
                } else {
                    "Walk-in"
                }

                // Group cart items by course
                val courseGroups = cartItemsWithCourses

                // Send notification for each course that has items
                courseGroups.forEach { (courseId, cartItems) ->
                    val course = availableCourses.find { it.name == courseId.toString() }
                    val courseName = course?.name ?: "Starters"

                    // Extract just the Cart objects from CartItemWithCourse
                    val carts = cartItems.map { it.cart }

                    if (carts.isNotEmpty()) {
                        sendCoursesNotificationUseCase(
                            storeId = storeId,
                            courseName = courseName,
                            tableName = tableName,
                            carts = carts
                        ).collect { result ->
                            when (result) {
                                is Success -> {
                                    // Handle success - maybe show a toast or update UI
                                    println("Course notification sent successfully for $courseName")
                                }

                                is Fail -> {
                                    // Handle error - maybe show error message
                                    println("Failed to send course notification for $courseName: ${result.error.message}")
                                }

                                else -> {
                                    // Loading state
                                    println("Sending course notification for $courseName...")
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                println("Error sending courses notification: ${e.message}")
            }
        }
    }

    /**
     * Apply service charge to the current order
     */
    fun applyServiceCharge(state: ProductsScreenState) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables
        )
        if (currentTableId != null) {
            // Apply service charge to table-specific order
            val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
            updatedTableServiceCharge[currentTableId] = true

            // Update the order totals to include service charge
            val currentOrder = state.tableOrders[currentTableId] ?: Order()
            val orderNet =
                calculateTotalFromOrder(state = state).tableOrders[currentTableId]?.netPayable
                    ?: 0.0
            val serviceChargeAmount =
                (orderNet) * (getServiceChargePercentage() / 100.0)
            val updatedOrder = currentOrder.copy(
                totalPrice = orderNet + (currentOrder.tax
                    ?: 0.0) + serviceChargeAmount
            )
            val updatedTableOrders = state.tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            setState {
                copy(
                    tableServiceChargeApplied = updatedTableServiceCharge,
                    tableOrders = updatedTableOrders,
                    showCart = true // Ensure cart is visible when service charge is applied
                )
            }
        } else {
            val orderNet = calculateTotalFromOrder(state = state).order.netPayable ?: 0.0
            // Apply service charge to global order
            val serviceChargeAmount =
                (orderNet) * (getServiceChargePercentage() / 100.0)
            val updatedOrder = state.order.copy(
                totalPrice = orderNet + (state.order.tax
                    ?: 0.0) + serviceChargeAmount
            )
            setState {
                copy(
                    serviceChargeApplied = true,
                    order = updatedOrder,
                    showCart = true // Ensure cart is visible when service charge is applied
                )
            }
        }
    }

    fun calculateTotalFromOrder(state: ProductsScreenState): ProductsScreenState {
        val tableId = state.getCurrentTableId()
        val order = if (tableId != null) {
            state.tableOrders[tableId] ?: Order()
        } else {
            state.order
        }
        val netPayable = order.carts?.sumByDouble {
            calculateTotal(
                stockItem = it.storeItem ?: StoreItem(),
                optionSets = it.storeItem?.optionSets ?: mutableListOf(),
                updatedStock = it.quantity ?: 0
            ).billAmount ?: 0.0
        } ?: 0.0
        val totalTax = order.carts?.sumByDouble { it.tax ?: 0.0 } ?: 0.0
        val totalPrice = netPayable + totalTax
        val updatedOrder = order.copy(
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        val updatedState = if (tableId != null) {
            val updatedTableOrders = state.tableOrders.toMutableMap()
            updatedTableOrders[tableId] = updatedOrder
            setState {
                copy(
                    tableOrders = updatedTableOrders
                )
            }
            state.copy(
                tableOrders = updatedTableOrders
            )
        } else {
            setState {
                copy(
                    order = updatedOrder
                )
            }
            state.copy(
                order = updatedOrder
            )
        }
        return updatedState
    }

    /**
     * Remove service charge from the current order
     */
    fun removeServiceCharge(state: ProductsScreenState) {
        val currentTableId = getCurrentTableId(
            selectedTables = state.selectedTables,
            selectedTableIndex = state.selectedTableIndex
        )
        if (currentTableId != null) {
            // Remove service charge from table-specific order
            val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
            updatedTableServiceCharge[currentTableId] = false

            // Track that user manually removed service charge for this table
            val updatedTableServiceChargeManuallyRemoved =
                state.tableServiceChargeManuallyRemoved.toMutableMap()
            updatedTableServiceChargeManuallyRemoved[currentTableId] = true

            // Update the order totals to remove service charge
            val currentOrder = state.tableOrders[currentTableId] ?: Order()
            val updatedOrder = currentOrder.copy(
                totalPrice = (currentOrder.netPayable ?: 0.0) + (currentOrder.tax ?: 0.0)
            )
            val updatedTableOrders = state.tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            setState {
                copy(
                    tableServiceChargeApplied = updatedTableServiceCharge,
                    tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
                    tableOrders = updatedTableOrders
                )
            }
        } else {
            // Remove service charge from global order
            val updatedOrder = state.order.copy(
                totalPrice = (state.order.netPayable ?: 0.0) + (state.order.tax ?: 0.0)
            )
            setState {
                copy(
                    serviceChargeApplied = false,
                    order = updatedOrder
                )
            }
        }
    }

    /**
     * Get service charge percentage from store configurations
     */
    fun getServiceChargePercentage(): Double {
        return prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
    }

    /**
     * Check if service charge should be auto-applied based on store configuration
     */
    fun shouldAutoApplyServiceCharge(): Boolean {
        return prefs.storeConfigurations?.data?.serviceChargeEnabledDefault == true
    }

    /**
     * Initialize service charge for a new table based on store configuration
     * Only auto-apply if user hasn't manually removed service charge for this table before
     */
    fun initializeServiceChargeForTable(tableId: Int) {
        if (shouldAutoApplyServiceCharge()) {
            setState {
                val updatedTableServiceCharge = tableServiceChargeApplied.toMutableMap()
                // Only auto-apply if user hasn't manually removed service charge for this table
                val wasManuallyRemoved = tableServiceChargeManuallyRemoved[tableId] ?: false
                if (!wasManuallyRemoved) {
                    updatedTableServiceCharge[tableId] = true
                }
                copy(tableServiceChargeApplied = updatedTableServiceCharge)
            }
        }
    }

    fun updateSelectedCartTab(cartTab: CartTab, currentTableId: Int?, state: ProductsScreenState) {
        if (currentTableId != null) {
            val tab = state.selectedTableBasedCartTab.toMutableMap()
            tab[currentTableId] = cartTab
            setState {
                copy(selectedTableBasedCartTab = tab)
            }
        } else {
            setState {
                copy(cartTab = cartTab)
            }
        }
    }

    /**
     * Reset the manual removal tracking for a specific table
     * This allows service charge to be auto-applied again for this table
     */
    fun resetServiceChargeManualRemovalForTable(tableId: Int) {
        setState {
            val updatedTableServiceChargeManuallyRemoved =
                tableServiceChargeManuallyRemoved.toMutableMap()
            updatedTableServiceChargeManuallyRemoved.remove(tableId)
            copy(tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved)
        }
    }

    fun updateAppliedPrefill(cart: Cart?, state: ProductsScreenState): ProductsScreenState {
        setState {
            copy(appliedPrefill = cart)
        }
        return state.copy(
            appliedPrefill = cart
        )
    }

    suspend fun printSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        setState {
            copy(printSalesReportResponse = Loading(), salesRequest = request)
        }
        printSalesReportUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(printSalesReportResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(printSalesReportResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<ProductsScreenViewModel, ProductsScreenState> {
        override fun create(state: ProductsScreenState): ProductsScreenViewModel
    }

    companion object :
        MavericksViewModelFactory<ProductsScreenViewModel, ProductsScreenState> by hiltMavericksViewModelFactory()
}